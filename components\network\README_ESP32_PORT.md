# ESP32 网络模块移植说明

## 已完成的改写

### 1. 系统调用替换
- 将 Linux 的 `pthread` 替换为 FreeRTOS 的 `xTaskCreate`
- 将 `printf/perror` 替换为 ESP-IDF 的 `ESP_LOG*` 系列
- 将 `exit()` 替换为 `vTaskDelete(NULL)`
- 将 `sleep/usleep` 替换为 `vTaskDelay(pdMS_TO_TICKS())`

### 2. 网络头文件替换
- 将 Linux 特定的网络头文件替换为 lwIP 和 ESP-IDF 网络头文件
- 保留标准的 socket API，因为 ESP-IDF 支持 POSIX socket

### 3. 网络接口处理
- 将 Linux 的 `struct ifreq` 替换为自定义的 `esp_ifreq_t`
- 网卡绑定操作暂时注释，因为 ESP32 的实现方式不同

### 4. 文件改动列表
- `CMakeLists.txt`: 添加了 ESP-IDF 组件依赖，添加编译选项忽略指针符号警告
- `tcp_client.h/c`: 完整移植到 FreeRTOS 任务
- `udp_client.h/c`: 完整移植到 FreeRTOS 任务
- `multicast.h/c`: 完整移植到 FreeRTOS 任务
- `netTools.h/c`: 完整移植，使用 ESP32 WiFi API 替代 Linux 网络接口
- `network_process.h/c`: 完整移植，使用 FreeRTOS 队列和信号量
- `recv_stream.h/c`: 完整移植，使用 FreeRTOS 任务和 ESP32 定时器

### 5. 编译问题修复
- 修复了 `g_media_name` 的指针符号警告（unsigned char* -> char*）
- 在 CMakeLists.txt 中添加了 `-Wno-pointer-sign` 等编译选项
- 添加了兼容性函数保持接口一致性

## 待完成的工作 (标记为 TODO)

### 1. 外部模块集成
需要在主程序中定义以下外部变量和函数：

#### 变量定义
```c
// 网络配置相关
int g_network_mode = NETWORK_MODE_LAN;
int g_system_work_mode = 0;
int g_sysBootType = 0;
int g_media_source = SOURCE_NULL;
unsigned char g_mac_addr[6] = {0};

// TCP连接相关
char g_host_tcp_addr_domain[64] = "";
char g_host_tcp_addr_domain2[64] = "";
int g_host_tcp_port = DEFAULT_TCP_PORT;
int g_host_tcp_port2 = DEFAULT_TCP_PORT;
int g_current_connect_tcp_id = 1;
char g_host_tcp_prase_ipAddress[64] = "";

// 网络状态相关
int eth_link_status = 1;
int g_module_4G_status = MODULE_4G_OFF;
```

#### 函数实现
```c
// 网络处理函数
extern unsigned char Calculate_XorDat(unsigned char *Data, int Length);
extern void NetPkg_Add(int NetType, unsigned char *buf, unsigned int len);
extern void Set_zone_idle_status(unsigned char *rxbuf, const char *function, int line, bool canRepeat_paly);

// 网络协议处理函数  
extern void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_buf);
extern void host_control_reboot(unsigned char *pkg_buf, unsigned char netType);
extern void Handle_Multicast_CMD(unsigned char *pkg_buf);
// ... 更多协议处理函数
```

### 2. 网络初始化
在 `app_main()` 中需要添加：
```c
// ESP-IDF 网络初始化
esp_netif_init();
esp_event_loop_create_default();

// WiFi 或以太网初始化
// ...

// 启动网络模块
start_udp_client();
start_multicast_cmd_client();
TCP_Client_Start();
```

### 3. 网络接口优化
- 完善 ESP32 特定的网络接口检测
- 实现正确的 MAC 地址设置和获取
- 添加 WiFi 和以太网的自动切换逻辑

### 4. 错误处理
- 完善网络错误处理和重连机制
- 添加网络状态监控和回调

### 5. 性能优化
- 调整任务堆栈大小和优先级
- 优化缓冲区大小适配 ESP32 内存限制

## 注意事项

1. **内存限制**: ESP32 内存有限，可能需要调整缓冲区大小
2. **任务优先级**: 需要根据实际应用调整网络任务的优先级
3. **网络配置**: 需要根据具体硬件配置选择 WiFi 或以太网
4. **线程安全**: 确保多任务环境下的线程安全访问

## 编译说明

确保在 `sdkconfig` 中启用：
- `CONFIG_LWIP_*` 相关网络配置
- `CONFIG_ESP_WIFI_*` WiFi 相关配置（如果使用 WiFi）
- `CONFIG_ETH_*` 以太网相关配置（如果使用以太网） 