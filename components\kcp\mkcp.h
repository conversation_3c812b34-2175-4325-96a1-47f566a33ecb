#ifndef __MKCP_H__
#define __MKCP_H__

#include "ikcp.h"

#ifdef __unix
#include <unistd.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <sys/types.h>
#endif

typedef struct {
	ikcpcb ikcp;
    unsigned long int  last_shake_time; //上一次握手时间

}_stkcpInfo;
extern _stkcpInfo stkcpComm;

/* get system time */
static inline void itimeofday(long *sec, long *usec)
{
	#if defined(__unix)
	struct timeval time;
	gettimeofday(&time, NULL);
	if (sec) *sec = time.tv_sec;
	if (usec) *usec = time.tv_usec;
	#else
	static long mode = 0, addsec = 0;
	bool retval;
	static IINT64 freq = 1;
	IINT64 qpc;
	if (mode == 0) {
		retval = QueryPerformanceFrequency((LARGE_INTEGER*)&freq);
		freq = (freq == 0)? 1 : freq;
		retval = QueryPerformanceCounter((LARGE_INTEGER*)&qpc);
		addsec = (long)time(NULL);
		addsec = addsec - (long)((qpc / freq) & 0x7fffffff);
		mode = 1;
	}
	retval = QueryPerformanceCounter((LARGE_INTEGER*)&qpc);
	retval = retval * 2;
	if (sec) *sec = (long)(qpc / freq) + addsec;
	if (usec) *usec = (long)((qpc % freq) * 1000000 / freq);
	#endif
}


/* get system tickcount millisecond */
static inline void GetTickCount(long *sec, long *nsec)
{
	#if defined(__unix)
	struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    if (sec) *sec = ts.tv_sec;
	if (nsec) *nsec = ts.tv_nsec;
	#endif
}

/* get clock in millisecond 64 */
static inline IINT64 iclock64(void)
{
	#if 0
	long s, u;
	IINT64 value;
	itimeofday(&s, &u);
	value = ((IINT64)s) * 1000 + (u / 1000);
	return value;
	#else
	long s, n;
	IINT64 value;
	GetTickCount(&s, &n);
	value = ((IINT64)s) * 1000 + (n / 1000000);
	return value;
	#endif
}

static inline IUINT32 iclock()
{
	return (IUINT32)(iclock64() & 0xfffffffful);
}


void mkcp_sendData(const char *buf,int len);
void exit_kcp_recv_thread();
void mkcp_init();
void mkcp_update();
void kcp_send_heartbeat();
#endif