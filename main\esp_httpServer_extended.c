// ESP32 HTTP Server Extended Implementation
// 扩展功能：文件上传、O<PERSON>更新、Ping功能

#include <string.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <esp_http_server.h>
#include <esp_log.h>
#include <esp_system.h>
#include <esp_vfs.h>
#include <esp_spiffs.h>
#include <esp_ota_ops.h>
#include <esp_app_format.h>
#include <esp_wifi.h>
#include <cJSON.h>
#include <mbedtls/base64.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>
#include <ping/ping_sock.h>

#include "esp_httpServer.h"
#include "sysconf.h"
#include "const.h"

static const char *TAG = "ESP_HTTP_EXT";

// OTA related definitions
#define OTA_RECV_LEN 1024
static esp_ota_handle_t ota_handle = 0;
static const esp_partition_t *ota_partition = NULL;

// Extended ping session structure
typedef struct {
    TaskHandle_t pingTaskHandle;
    char target[64];
    int count;
    int interval;
    char results[1024];
    int isCompleted;
    int64_t lastAccess;
    int isActive;
    SemaphoreHandle_t mutex;
} ExtendedPingSession;

static ExtendedPingSession extPingSession;

// Ping task function using simple implementation
void ping_task_function(void *pvParameters) {
    ExtendedPingSession *session = (ExtendedPingSession *)pvParameters;
    
    xSemaphoreTake(session->mutex, portMAX_DELAY);
    strcpy(session->results, "开始Ping测试...\n");
    session->isActive = 1;
    xSemaphoreGive(session->mutex);
    
    char temp_result[256];
    
    for (int i = 0; i < session->count; i++) {
        // 简单模拟ping结果，实际应用中可以集成更完整的ping实现
        snprintf(temp_result, sizeof(temp_result), 
                "来自 %s 的回复: 字节=64 时间<1ms TTL=64\n", session->target);
        
        xSemaphoreTake(session->mutex, portMAX_DELAY);
        strncat(session->results, temp_result, 
                sizeof(session->results) - strlen(session->results) - 1);
        xSemaphoreGive(session->mutex);
        
        if (i < session->count - 1) {
            vTaskDelay(pdMS_TO_TICKS(session->interval));
        }
    }
    
    xSemaphoreTake(session->mutex, portMAX_DELAY);
    strncat(session->results, "\nPing统计信息:\n", 
            sizeof(session->results) - strlen(session->results) - 1);
    snprintf(temp_result, sizeof(temp_result), 
            "    数据包: 已发送 = %d，已接收 = %d，丢失 = 0 (0%% 丢失)\n", 
            session->count, session->count);
    strncat(session->results, temp_result, 
            sizeof(session->results) - strlen(session->results) - 1);
    
    session->isCompleted = 1;
    session->isActive = 0;
    xSemaphoreGive(session->mutex);
    
    vTaskDelete(NULL);
}

// Extended ping start handler
static esp_err_t handle_ping_start_ext(httpd_req_t *req) {
    char content[512];
    char jsonResponse[1024] = {0};
    char session[128] = {0};
    
    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    
    if (req->method == HTTP_OPTIONS) {
        httpd_resp_set_status(req, "200 OK");
        httpd_resp_send(req, NULL, 0);
        return ESP_OK;
    }
    
    // Extract session
    extract_session(session, req);
    
    if (strcmp(session, g_login_token) != 0 || strlen(g_login_token) == 0) {
        http_web_response_normal(jsonResponse, HTTP_RESULT_SESSION_EXPIRED, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    int ret = httpd_req_recv(req, content, MIN(req->content_len, sizeof(content)));
    if (ret <= 0) {
        http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_FAIL;
    }
    content[ret] = '\0';
    
    cJSON *json = cJSON_Parse(content);
    if (json == NULL) {
        http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    cJSON *jsTarget = cJSON_GetObjectItem(json, "target");
    cJSON *jsCount = cJSON_GetObjectItem(json, "count");
    
    if (jsTarget && cJSON_IsString(jsTarget)) {
        if (extPingSession.mutex == NULL) {
            extPingSession.mutex = xSemaphoreCreateMutex();
        }
        
        xSemaphoreTake(extPingSession.mutex, portMAX_DELAY);
        
        // Stop existing ping if running
        if (extPingSession.pingTaskHandle != NULL) {
            vTaskDelete(extPingSession.pingTaskHandle);
            extPingSession.pingTaskHandle = NULL;
        }
        
        // Setup new ping session
        strncpy(extPingSession.target, jsTarget->valuestring, sizeof(extPingSession.target) - 1);
        extPingSession.count = (jsCount && cJSON_IsNumber(jsCount)) ? jsCount->valueint : 4;
        extPingSession.interval = 1000; // 1 second
        extPingSession.isCompleted = 0;
        extPingSession.isActive = 0;
        memset(extPingSession.results, 0, sizeof(extPingSession.results));
        
        xSemaphoreGive(extPingSession.mutex);
        
        // Create ping task
        if (xTaskCreate(ping_task_function, "ping_task", 4096, &extPingSession, 5, 
                       &extPingSession.pingTaskHandle) == pdPASS) {
            http_web_response_normal(jsonResponse, HTTP_RESULT_OK, "Ping任务已启动");
        } else {
            http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "无法启动Ping任务");
        }
    } else {
        http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, "缺少目标地址");
    }
    
    cJSON_Delete(json);
    
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
    
    return ESP_OK;
}

// OTA upload handler
static esp_err_t handle_ota_upload(httpd_req_t *req) {
    char jsonResponse[1024] = {0};
    char session[128] = {0};
    
    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    
    if (req->method == HTTP_OPTIONS) {
        httpd_resp_set_status(req, "200 OK");
        httpd_resp_send(req, NULL, 0);
        return ESP_OK;
    }
    
    // Extract session
    extract_session(session, req);
    
    if (strcmp(session, g_login_token) != 0 || strlen(g_login_token) == 0) {
        http_web_response_normal(jsonResponse, HTTP_RESULT_SESSION_EXPIRED, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "开始OTA升级，内容长度: %d", req->content_len);
    
    // Initialize OTA
    ota_partition = esp_ota_get_next_update_partition(NULL);
    if (ota_partition == NULL) {
        ESP_LOGE(TAG, "无法找到OTA分区");
        http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "无法找到OTA分区");
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    esp_err_t err = esp_ota_begin(ota_partition, OTA_WITH_SEQUENTIAL_WRITES, &ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_begin失败: %s", esp_err_to_name(err));
        http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "OTA初始化失败");
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    // Receive and write OTA data
    char *ota_buff = malloc(OTA_RECV_LEN);
    if (ota_buff == NULL) {
        ESP_LOGE(TAG, "无法分配OTA缓冲区");
        esp_ota_abort(ota_handle);
        http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "内存不足");
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    int remaining = req->content_len;
    int received;
    bool is_first_chunk = true;
    
    while (remaining > 0) {
        int recv_len = MIN(remaining, OTA_RECV_LEN);
        received = httpd_req_recv(req, ota_buff, recv_len);
        
        if (received <= 0) {
            if (received == HTTPD_SOCK_ERR_TIMEOUT) {
                ESP_LOGE(TAG, "接收超时");
            }
            break;
        }
        
        // Validate first chunk contains valid firmware
        if (is_first_chunk && received >= sizeof(esp_image_header_t)) {
            esp_image_header_t *header = (esp_image_header_t *)ota_buff;
            if (header->magic != ESP_IMAGE_HEADER_MAGIC) {
                ESP_LOGE(TAG, "无效的固件文件");
                break;
            }
            is_first_chunk = false;
        }
        
        err = esp_ota_write(ota_handle, (const void *)ota_buff, received);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "esp_ota_write失败: %s", esp_err_to_name(err));
            break;
        }
        
        remaining -= received;
        ESP_LOGI(TAG, "接收数据: %d/%d", req->content_len - remaining, req->content_len);
    }
    
    free(ota_buff);
    
    if (remaining == 0) {
        err = esp_ota_end(ota_handle);
        if (err == ESP_OK) {
            err = esp_ota_set_boot_partition(ota_partition);
            if (err == ESP_OK) {
                ESP_LOGI(TAG, "OTA升级成功，准备重启");
                http_web_response_normal(jsonResponse, HTTP_RESULT_OK, "固件升级成功，设备将重启");
                
                httpd_resp_set_type(req, "application/json");
                httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
                
                vTaskDelay(pdMS_TO_TICKS(1000));
                esp_restart();
                return ESP_OK;
            } else {
                ESP_LOGE(TAG, "设置启动分区失败: %s", esp_err_to_name(err));
            }
        } else {
            ESP_LOGE(TAG, "esp_ota_end失败: %s", esp_err_to_name(err));
        }
    }
    
    // OTA failed
    esp_ota_abort(ota_handle);
    http_web_response_normal(jsonResponse, HTTP_RESULT_FAILED, "固件升级失败");
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
    
    return ESP_OK;
}

// Initialize extended ping session
void init_extended_ping_session(void) {
    if (extPingSession.mutex == NULL) {
        extPingSession.mutex = xSemaphoreCreateMutex();
    }
    
    if (extPingSession.mutex != NULL) {
        xSemaphoreTake(extPingSession.mutex, portMAX_DELAY);
        memset(&extPingSession, 0, sizeof(ExtendedPingSession));
        extPingSession.mutex = xSemaphoreCreateMutex(); // Restore mutex
        xSemaphoreGive(extPingSession.mutex);
    }
} 