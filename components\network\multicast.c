/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:19:32 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-16 17:52:10
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"
#include "esp_timer.h"
#include <stdlib.h>  // 添加malloc/free支持

#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "udp_client.h"
#include "tcp_client.h"
// TODO: 引用外部模块
// #include "../kcp/mkcp.h"
#include "multicast.h"

static const char *TAG = "MULTICAST";

static int multicast_send_socket=-1;
static int multicast_send_netTools_socket=-1;	//新增，原来和multicast_send_socket是一样的
static struct sockaddr_in multi_send_sockAddr;
static struct sockaddr_in multi_send_netTools_sockAddr;	//新增，原来和multi_send_sockAddr是一样的

struct sockaddr_in multi_host_addr;

static int multicast_sockfd = -1;

/*********************************************************************
 * @fn      multicast_send_data
 *
 * @brief  	发送组播数据
 *
 * @param   data--数据缓冲区
 * 			len--数据长度
 *
 * @return  none
*********************************************************************/
void multicast_send_data(unsigned char * data,unsigned short len)  
{
   if(multicast_send_socket == -1)
   {
       multicast_send_socket = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&multi_send_sockAddr, 0, sizeof(multi_send_sockAddr));
       multi_send_sockAddr.sin_family = AF_INET;
       multi_send_sockAddr.sin_port = htons (MCAST_CMD_SEND_PORT);
       inet_pton(AF_INET, MCAST_CMD_SEND_ADDR, &multi_send_sockAddr.sin_addr);
   }
   if(multicast_send_netTools_socket == -1)
   {
       multicast_send_netTools_socket = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&multi_send_netTools_sockAddr, 0, sizeof(multi_send_netTools_sockAddr));
       multi_send_netTools_sockAddr.sin_family = AF_INET;
       multi_send_netTools_sockAddr.sin_port = htons (MCAST_NETTOOLS_CMD_SEND_PORT);
       inet_pton(AF_INET, MCAST_NETTOOLS_CMD_SEND_ADDR, &multi_send_netTools_sockAddr.sin_addr);
   }

   int sendLen=sendto(multicast_send_socket, data, len, 0,(struct sockaddr*)&multi_send_sockAddr, sizeof(multi_send_sockAddr));
   sendLen=sendto(multicast_send_netTools_socket, data, len, 0,(struct sockaddr*)&multi_send_netTools_sockAddr, sizeof(multi_send_netTools_sockAddr));

   if(sendLen == -1)
   {
       ESP_LOGE(TAG, "multicast_send_data failed: %s", strerror(errno));
   }
}


/*********************************************************************
 * @fn      power_on_notice_host
 *
 * @brief   开机首次发送在线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void power_on_notice_host(void)
{
	int i;
	int payloadSize = 0;
	// TODO: 使用动态分配减少栈使用 - 原来data_buf[MAX_BUF_SIZE]占用1100字节栈空间
	unsigned char *data_buf = malloc(128);
	unsigned char send_buf[256];

	if (data_buf == NULL) {
		ESP_LOGE(TAG, "Failed to allocate memory for data_buf in power_on_notice_host");
		return;
	}

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存
	memset(data_buf, 0x00, 128);
	// 添加命令字
	send_buf[0] = (unsigned char)(CMD_CLIENT_POWER_ON_NOTICE_HOST>>8);
	send_buf[1] = (unsigned char)CMD_CLIENT_POWER_ON_NOTICE_HOST;
	// 添加包序号
	send_buf[2] = 0;
	// 保留位

	send_buf[3] = g_system_work_mode << 2;
	send_buf[3] = send_buf[3] | g_network_mode << 4;
	send_buf[3] = send_buf[3] | 0x01 << 6;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = 0x00;

	//20230602 启动类型
	send_buf[PAYLOAD_START+payloadSize] = g_sysBootType;
	payloadSize++;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);
	// 发送数据
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
               // CMD_CLIENT_POWER_ON_NOTICE_HOST
	}
	else
	{
		#if ENABLE_TCP_CLIENT
		host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
		#endif
	}
    ESP_LOGI(TAG, "[power_on_notice_host]");

    // 释放动态分配的内存
    free(data_buf);
}


/*********************************************************************
 * @fn      Send_Unonline_Info
 *
 * @brief   发送离线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void Send_Unonline_Info(void)
{
	int i = 0;
	int pos = 0;
	// TODO: 使用动态分配减少栈使用 - 原来data_buf[MAX_BUF_SIZE]占用1100字节栈空间
	unsigned char *data_buf = malloc(128);
	unsigned char send_buf[256];

	if (data_buf == NULL) {
		ESP_LOGE(TAG, "Failed to allocate memory for data_buf in Send_Unonline_Info");
		return;
	}

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存
	memset(data_buf, 0x00, 128);
	// 添加命令字
	send_buf[pos++] = (unsigned char)(CMD_OFFLINE>>8);
	send_buf[pos++] = (unsigned char)CMD_OFFLINE;
	// 添加包序号
	send_buf[pos++] = 0;
	// 保留位

	send_buf[pos++] = RESERVE;

	// 产品型号
	send_buf[pos++] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x06;
	for (i=0; i<6; i++)
	{
		send_buf[pos++] = g_mac_addr[i];
	}
	send_buf[pos++] = Calculate_XorDat(&send_buf[PAYLOAD_START], 6);
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, pos);
	}
	else
	{
		#if ENABLE_TCP_CLIENT
		host_tcp_send_data(send_buf, pos);
		#endif
		host_udp_send_data(send_buf, pos);
	}

	// 释放动态分配的内存
	free(data_buf);
}


/*********************************************************************
 * @fn      send_online_info
 *
 * @brief   发送在线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void send_online_info(void)
{
	int i;
	int mac_byte_pos=0;
	int payloadSize = 0;
	// TODO: 使用动态分配减少栈使用 - 原来data_buf[MAX_BUF_SIZE]占用1100字节栈空间
	unsigned char *data_buf = malloc(128);
	unsigned char send_buf[256];

	if (data_buf == NULL) {
		ESP_LOGE(TAG, "Failed to allocate memory for data_buf");
		return;
	}

	memset(send_buf, 0x00, sizeof(send_buf));
	memset(data_buf, 0x00, 128);
	// 添加命令字
	send_buf[0] = (unsigned char)(CMD_ONLINE>>8);
	send_buf[1] = (unsigned char)CMD_ONLINE;
	// 添加包序号
	send_buf[2] = 0;
	// 保留位

	send_buf[3] = g_system_work_mode << 2;
	send_buf[3] = send_buf[3] | g_network_mode << 4;
	send_buf[3] = send_buf[3] | 0x01 << 6;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = 0x00;

	// 添加设备别名
	if (strlen(g_device_alias) > 0)
	{
		send_buf[PAYLOAD_START] = strlen(g_device_alias);
		payloadSize += 1;
		for (i=0; i<strlen(g_device_alias); i++)
		{
			send_buf[PAYLOAD_START+payloadSize+i] = g_device_alias[i];
		}
		payloadSize += strlen(g_device_alias);
	}
	else
	{
		send_buf[PAYLOAD_START] = 0x00;
		payloadSize += 1;
	}

	// 添加终端ID（即MAC地址）
	//get_eth_macaddr(data_buf);
	send_buf[PAYLOAD_START+payloadSize] = 6;
	payloadSize += 1;
	mac_byte_pos = PAYLOAD_START+payloadSize;
	for (i=0; i<6; i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = g_mac_addr[i];
	}
	payloadSize += 6;

	// 添加音量
	if(g_paging_status == PAGING_START)
	{
		send_buf[PAYLOAD_START+payloadSize] = 100;//pager_property.volume;
	}
	else
	{
		send_buf[PAYLOAD_START+payloadSize] = g_system_volume;
	}
	payloadSize += 1;

	// 添加节目源
	send_buf[PAYLOAD_START+payloadSize] = g_media_source;
	payloadSize += 1;
	//节目名称长度

	int media_len=strlen((char*)g_media_name)>64?64:strlen((char*)g_media_name);
	send_buf[PAYLOAD_START+payloadSize] = media_len;
	payloadSize += 1;
	//节目名称
	for (i=0; i<media_len; i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = g_media_name[i];
	}
	payloadSize += media_len;

	//播放状态
	send_buf[PAYLOAD_START+payloadSize] = g_media_status;
	payloadSize += 1;
	//终端控制模式
	send_buf[PAYLOAD_START+payloadSize] = g_terminal_control_mode;
	payloadSize += 1;


	// 添加版本信息
	sprintf(data_buf, "%s", LOCAL_FIRMWARE_VERSION);
	#if LZY_COMMERCIAL_VERSION
	strcat(data_buf,"L");
	#endif
	#if AIPU_VERSION
	strcat(data_buf,"AP");
	#endif
	
	if(g_device_moduleId>0)
	{
		char tmp_moduleId[10]={0};
		sprintf(tmp_moduleId,"%d",g_device_moduleId);
		strcat(data_buf,"-");
		strcat(data_buf,tmp_moduleId);
	}
	
	send_buf[PAYLOAD_START+payloadSize] = strlen(data_buf);
	payloadSize += 1;
	for (i = 0; i<strlen(data_buf); i++)
	{
		send_buf[PAYLOAD_START+payloadSize+i] = data_buf[i];
	}
	payloadSize += strlen(data_buf);
    
	//设备特性
	unsigned char device_feature=0;
	send_buf[PAYLOAD_START+payloadSize] = device_feature;
    payloadSize++;

	//音频混音器信号状态
	send_buf[PAYLOAD_START+payloadSize] = 0;
	payloadSize++;


	//4G模块信号质量
	send_buf[PAYLOAD_START+payloadSize] = 0;		//0或者99都代表未知，使用的是非4G模块或者是4G模块但使用了有线网卡
	payloadSize++;

	//4G模块卡号长度
	int iccid_length=0;
	send_buf[PAYLOAD_START+payloadSize] = iccid_length;		//0或者99都代表未知，使用非4G模块或者，或者4G模块使用了有线通讯
	payloadSize++;
	//4G模块卡号
	payloadSize+=iccid_length;

	//用户名(目前仅寻呼台用到)
	int userLen=0;
	char userName[33]={0};
	send_buf[PAYLOAD_START+payloadSize] = userLen;
	payloadSize++;
	memcpy(send_buf+PAYLOAD_START+payloadSize,userName,userLen);
	payloadSize+=userLen;

	//音频采集器信号状态
	send_buf[PAYLOAD_START+payloadSize] = 0;
	payloadSize++;

	//电话网关信号状态
	send_buf[PAYLOAD_START+payloadSize] = 0;
	payloadSize++;

	//新增字段，支持新的播放指令(加入命令字)
	bool hasExtraFeature=false;
	if(!hasExtraFeature)
	{
		//printf("has No ExtraFeature!!!\n");
		send_buf[PAYLOAD_START+payloadSize] = 0;	//额外的特性，0表示没有额外的特性
	}
	payloadSize++;
	

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);
	// 发送数据
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else
	{
		#if ENABLE_TCP_CLIENT

		if(sysRunTime>=10)    				//开机前10s不发送组播信息
        {
            multicast_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
        }

		if(tcp_get_master_connect_status() == 1)
		{
			#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
			{
				if(!tcp_get_master_client_fr_flag())    //第一次还未应答
				{
					send_buf[0] = (unsigned char)(CMD_TCP_CLIENT_CONNECTED>>8);
					send_buf[1] = (unsigned char)CMD_TCP_CLIENT_CONNECTED;
					tcp_set_master_client_fr_flag(1);
				}
			}
			#endif
			//TODO
			host_tcp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
			//kcp_send_heartbeat();
		}
        #endif
	}

    ESP_LOGI(TAG, "[send_online_info]");

    // 释放动态分配的内存
    free(data_buf);
}


/*********************************************************************
 * @fn      Handle_Multicast_CMD
 *
 * @brief  	处理组播命令字
 *
 * @param   NULL
 *
 * @return  none
*********************************************************************/
void Handle_Multicast_CMD(unsigned char *rxbuf)
{       
	//判断自己是否处于MAC列表中
	int pos=PAYLOAD_START;
	int zone_num=rxbuf[pos++];
	unsigned char *mac_buf = &rxbuf[pos];

	int i=0,t=0;
	int mac_ok=0;
	for(i=0;i<zone_num;i++)
	{
		if(memcmp(g_mac_addr,mac_buf+i*6,6) == 0)
		{
			mac_ok=1;
			break;
		}
		#if IS_DEVICE_AUDIO_MIXER
		if(memcmp(g_mixer_mac_addr,mac_buf+i*6,6) == 0)
		{
			mac_ok=1;
			break;
		}
		#endif
	}
	if(!mac_ok)
	{
		return;
	}

	pos+=zone_num*6;

	//MAC匹配，校验通过
	//获取两个字节的命令字
	int cmd_word = rxbuf[pos]*256 + rxbuf[pos+1];
	pos+=2;
	int payload_length = rxbuf[pos]*256 + rxbuf[pos+1];
	pos+=2;
	unsigned char *cmd_buf=&rxbuf[pos];
	
	ESP_LOGI(TAG, "Handle_Multicast_CMD:0x%04x",cmd_word);
	
	//判断是主机还是寻呼台发过来的组播指令,
	switch(cmd_word)
	{
		case CMD_PAGING_NOTIFY:			//寻呼通知
			//pkg_paging_notification(cmd_buf,payload_length,DEVICE_MODEL_PAGING,0);
			break;
	}
	
}


/*********************************************************************
 * @fn      Multicast_Pkg_Process
 *
 * @brief   命令处理
 *
 * @param   pkg_buf   - 包缓存
 *          pkg_len -  包长度
 *
 * @return	void
 *********************************************************************/
void Multicast_Pkg_Process(unsigned char *pkg_buf, int pkg_len)
{
	int cmd_word = 0;
	// 获取两个字节的命令字
	cmd_word = (pkg_buf[0])*256 + pkg_buf[1];
	//Dbg("multicast cmd_word:0x%04x\n", cmd_word);

	int device_model = pkg_buf[4];
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		if( device_model != DEVICE_MODEL_HOST && device_model != DEVICE_MODEL_MOBILE
		   && device_model != DEVICE_MODEL_PAGING && device_model != DEVICE_MODEL_NETWORK_TOOLS)
		{
			return;
		}

		if(device_model == DEVICE_MODEL_HOST)
		{
			g_host_device_TimeOut=0;
		}
	}
	else if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(device_model != DEVICE_MODEL_NETWORK_TOOLS && cmd_word!=CMD_HOST_QUERY_SET_IP_INFO)
		{
			return;
		}
	}

	//ESP_LOGI(TAG, "Multicast_Pkg_Process:cmd_word=0x%04x",cmd_word);

	switch(cmd_word)
	{
		case CMD_SEARCH_DEVICE://搜索在线终端设备
			send_online_info();
			if(device_model == DEVICE_MODEL_HOST)
			{
				if(g_network_mode == NETWORK_MODE_LAN)
				{
					if(power_on_flag==0)
					{
						power_on_flag = 1;
						power_on_notice_host();
					}
				}
			}
		break;
		case CMD_TIME_SYN://时间同步
			pkg_set_local_time(pkg_buf);
		break;
		case CMD_HOST_QUERY_SET_IP_INFO://主机向终端设置IP属性
			HOST_QUERY_SET_IP_INFO(pkg_buf);
		break;
		case CMD_SEND_PAGING_NOTIFY_MULTICAST://通知终端开始寻呼(组播)
		break;

		case CMD_CONTROL_REBOOT_MULTICAST://主机向终端发送重启指令(组播)
			host_control_reboot(pkg_buf, 1);
		break;

		case CMD_SEND_MULTICAST_CMD:			//控制设备（包括主机）组播命令给终端(包括寻呼台发起寻呼通知）
			//Handle_Multicast_CMD(pkg_buf);
		break;
	
		case CMD_HOST_SET_DSP_FIRMWARE_FEATURE:	//主机或配置工具向终端查询/设置DSP固件功能特性（组播）
		#if(IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL)
			Host_Set_Dsp_Firmware_Feature(pkg_buf);
		#endif
		break;
		
		case CMD_HOST_SET_SERIAL_NUMBER:		//主机或配置工具向终端获取设备序列号（组播)
			Host_Set_SerialNumber(pkg_buf);
		break;

		case CMD_HOST_QUERY_SET_NET_WORK_MODE:	//主机向终端设置网络模式
		case CMD_NETWORK_MODE_MULTICAST:
			ProcessHostSetNetWork(pkg_buf,pkg_len,true);
		break;

		case CMD_SEND_TOOLS_DEVICE_EXTENSION:
			//procTools_Set_Extension(pkg_buf,pkg_len);
		break;

		case CMD_SEND_TOOLS_PRE_DEVICE_EXTENSION:
			//procTools_PreSet_Extension(pkg_buf,pkg_len);
		break;
	}
}

/*********************************************************************
 * @fn      host_udp_client
 *
 * @brief   创建一个组播线程来处理主机的请求
 *
 * @param   void
 *
 * @return  none
 */
static void multicast_cmd_client(void *pvParameters)
{
	int ret, i;
	int Rxlen;
	int Index = 0;
    int Pkg_Length = 0;
    int Payload_Length = 0;
	fd_set readfd;
	int Read_Size = MAX_BUF_SIZE;
	unsigned char RxBuf[MAX_BUF_SIZE];
	struct sockaddr_in Host_Addr;
	socklen_t g_sockaddr_len = sizeof(struct sockaddr_in);
    struct ip_mreq mreq;     /* 创建 socket 用于UDP通讯 */
	
	/*填充地址信息*/
	memset(&Host_Addr, 0, sizeof(Host_Addr));         // 清空地址信息
	Host_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
	Host_Addr.sin_port = htons(MCAST_CMD_RECV_PORT);  // 注册端口
	
	/*创建套接字*/
	if ((multicast_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		ESP_LOGE(TAG, "multicast_sockfd create error: %s", strerror(errno));
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "multicast_sockfd create success!");
	}

     /* 设置要加入组播的地址 */
	 memset(&mreq, 0,sizeof (struct ip_mreq));
	 inet_pton(AF_INET,MCAST_CMD_RECV_ADDR,&Host_Addr.sin_addr);
	 /* 设置组地址 */
	 memcpy (&mreq.imr_multiaddr.s_addr,&Host_Addr.sin_addr.s_addr, sizeof (struct in_addr));
	 /* 设置发送组播消息的源主机的地址信息 */
	 mreq.imr_interface.s_addr = htonl (INADDR_ANY);
	 /* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	 if (setsockopt(multicast_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	 {
		ESP_LOGE(TAG, "multicast_sockfd SOCKET: %s", strerror(errno));
		close(multicast_sockfd);
		vTaskDelete(NULL);
		return;
	 }
     
	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(multicast_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		ESP_LOGE(TAG, "setsockopt SO_REUSEADDR failed: %s", strerror(errno));
		close(multicast_sockfd);
		vTaskDelete(NULL);
		return;
	}
	/*绑定地址到套接字*/
	ret = bind(multicast_sockfd, (struct sockaddr *)&Host_Addr, sizeof(Host_Addr));
	if (ret < 0)
	{
		ESP_LOGE(TAG, "multicast_sockfd call bind error: %s", strerror(errno));
		close(multicast_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "multicast_sockfd bind socket success!");
	}
	
	while(1)
	{	

		FD_ZERO(&readfd);                // 清空读文件描述集合
		FD_SET(multicast_sockfd, &readfd); // 注册套接字文件描述符
		int fdmax=multicast_sockfd;
		Rxlen=0;

		ret = select(fdmax+1, &readfd, NULL, NULL, NULL);
		switch(ret)
		{
			case -1 : // 调用出错
				ESP_LOGE(TAG, "multicast pthread call select error: %s", strerror(errno));
				break;
			
			case 0 : // 超时
				ESP_LOGD(TAG, "multicast pthread select timeout!!!");
				break;
				
			default : // 有数据可读
				if (FD_ISSET(multicast_sockfd, &readfd))
				{
					Rxlen = recvfrom(multicast_sockfd, &RxBuf[Index], Read_Size, 0, (struct sockaddr *)&Host_Addr, &g_sockaddr_len);
				}
				if (Rxlen < PACKAGE_MIN_SIZE)
				{
					ESP_LOGE(TAG, "pager udp recvfrom failed: %s", strerror(errno));
					memset(RxBuf, 0, MAX_BUF_SIZE);
					continue;
				}
				else
				{
						Payload_Length = (int)RxBuf[PAYLOAD_START-2]*256 + (int)RxBuf[PAYLOAD_START-1]; // 负载数据长度
						if(Payload_Length>MAX_BUF_SIZE-PACKAGE_MIN_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}
						Pkg_Length = Payload_Length + PACKAGE_MIN_SIZE;     // 整个包长
						if(Pkg_Length>MAX_BUF_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}

					/*判断接收长度是否等于包长*/
					if (Rxlen != Pkg_Length)
					{
						memset(RxBuf, 0, MAX_BUF_SIZE);
						continue;
					}
					else
					{
						// 计算校验和
						if (Calculate_XorDat(&RxBuf[PAYLOAD_START], Payload_Length) == RxBuf[Pkg_Length-1])
						{
							//ESP_LOGI(TAG, "multicast command:0x%04x",RxBuf[0]*256+RxBuf[1]);
							if(RxBuf[4] == DEVICE_MODEL_HOST)
							{
								multi_host_addr=Host_Addr;
								//printf("multi_host_addr1:%d.%d.%d.%d\n", (multi_host_addr.sin_addr.s_addr)&0xff, (multi_host_addr.sin_addr.s_addr>>8)&0xff,(multi_host_addr.sin_addr.s_addr>>16)&0xff,(multi_host_addr.sin_addr.s_addr>>24)&0xff);
							}
							NetPkg_Add(NET_TYPE_MULTICAST_SERVER,RxBuf,Pkg_Length);
						}
						else
						{
							ESP_LOGE(TAG, "multicast payload check error!!!");
						}
						memset(RxBuf, 0, MAX_BUF_SIZE);
					}
				}
				break;
		}
	}

	close(multicast_sockfd);
	vTaskDelete(NULL);
}

/*********************************************************************
 * @fn      start_multicast_cmd_client
 *
 * @brief   启动组播命令接收客户端
 *
 * @param
 *
 *
 * @return
 */
void start_multicast_cmd_client(void)
{
	BaseType_t ret;
    TaskHandle_t host_multicast_pthread;
	
	ret = xTaskCreate(multicast_cmd_client, "host_multicast_pthread", 4096, NULL, 5, &host_multicast_pthread);
	if (ret != pdPASS)
	{
		ESP_LOGE(TAG, "start_multicast_cmd_client create failed");
	}
	else
	{
		ESP_LOGI(TAG, "start_multicast_cmd_client succeed!");
	}
}