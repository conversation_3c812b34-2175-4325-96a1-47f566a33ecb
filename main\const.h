#ifndef CONST_H
#define CONST_H

#define PRJ_VER "V2.5.0506"

#define IS_RELEASE_VERSION  0          //是否发行版

#define CURRENT_DEVICE_MODEL MODEL_IP_SPEAKER_G

#if IS_RELEASE_VERSION
#undef IS_CLOSE_USB_UART_LOG
#define IS_CLOSE_USB_UART_LOG    1    //发行版需要关闭USB串口
#else
#undef IS_CLOSE_USB_UART_LOG
#define IS_CLOSE_USB_UART_LOG    0    //测试版才打开USB串口
#endif


#define PRJ_NAME "WifiSpeakerA"
#define FIRMWARE_VERISON "P201_WIFI_V02"

#define LOCAL_FIRMWARE_VERSION		"2.5.0626"		//固件版本号
#define DEVICE_VERSION				LOCAL_FIRMWARE_VERSION		//设备版本
#define HARDWARE_VERSION			"ESP32-V1.0"				//硬件版本

#define POWER_P32_V01	800

#define DEFAULT_MODULE_ID	POWER_P32_V01		//烧录包根据实际情况更改

#define IS_DEVICE_DECODER_TERMINAL 1		//是否是解码器

#define ENABLE_TCP_CLIENT 	1			//是否开启TCP客户端


#define INI_SECTION_BASIC   		"Basic"
#define INI_SECTION_DEVICE  		"Device"
#define INI_SETCION_NETWORK 		"Network"
#define INI_SETCION_DSP_Firmware 	"Firmware"
#define INI_SETCION_DSP_EQ 			"Eq"
#define INI_SETCION_MFR 			"Manufacturer"

#define INI_KEY_DSP_MODEL_ID         "MODULE_ID"
#define INI_KEY_DSP_AMIC0_SWITCH     "AMIC0_SWITCH"
#define INI_KEY_DSP_AMIC0_GAIN       "AMIC0_GAIN"
#define INI_KEY_DSP_AMIC1_SWITCH     "AMIC1_SWITCH"
#define INI_KEY_DSP_AMIC1_GAIN       "AMIC1_GAIN"
#define INI_KEY_DSP_AMIC2_SWITCH     "AMIC2_SWITCH"
#define INI_KEY_DSP_AMIC2_GAIN       "AMIC2_GAIN"
#define INI_KEY_DSP_DAC0L_SWITCH     "DAC0L_SWITCH"
#define INI_KEY_DSP_DAC0L_GAIN       "DAC0L_GAIN"
#define INI_KEY_DSP_DAC0R_SWITCH     "DAC0R_SWITCH"
#define INI_KEY_DSP_DAC0R_GAIN       "DAC0R_GAIN"


/********网络相关*****************/
#define NETWORK_MODE_LAN	1
#define NETWORK_MODE_WAN	2

#define IP_ASSIGN_DHCP 		0
#define IP_ASSIGN_STATIC    1

/************************/

#define HOST_TIMEOUT_VALUE		60	  //主机超时时间

/********优先级定义*******************/
enum
{
	#if LOCAL_SOURCE_PRIORITY_HIGHEST
	PRIORITY_AUX,
	#endif
  	PRIORITY_NET_PAGING,								//寻呼
	PRIORITY_FIRE_ALARM,								//火警信号
	PRIORITY_MONITOR_EV,								//监控事件
	PRIORITY_CALL,										//对讲
	PRIORITY_SIP,						    			//SIP对讲
	PRIORITY_AUDIO_MIXED,								//音频混音
	PRIORITY_PHONE_GATEWAY,								//电话网关
	PRIORITY_API_TTS_MUSIC,								//API播放TTS或者音乐
	PRIORITY_AUDIO_HIGH_PRIORITY,						//音频采集音源(高优先级)
	PRIORITY_TIMING,									//定時
	PRIORITY_AUDIO_COLLECTOR,							//音频采集音源
	PRIORITY_NET_PLAY,									//网络点播
	PRIORITY_100V,										//100v
	#if !LOCAL_SOURCE_PRIORITY_HIGHEST
	PRIORITY_AUX,										//AUX
	#endif
    PRIORITY_NULL,										//空闲
	PRIORITY_MAX_NUM									//MAX
};

typedef enum
{
	DF_BLUETOOTH		=   0X00000001,	// 蓝牙设备
    DF_LOCAL_MONITOR	=   0X00000002, // 本地监听
    DF_CALL             =   0X00000004, // 对讲
	DF_VIDEO            =   0x00000008, // 可视
	DF_SIP            	=   0x00000010, // SIP
	DF_INFORMATION_PUBLISH =   0x00000020 // 信息发布
}Device_Feature;

//设备拓展特性，包含消防采集C、电源时序器C、远程遥控器C、音频采集器C（如果P030主板，则默认支持，无需启用）、音频混音器C
typedef enum
{
	DF_EXTRA_FIRE_COLLECTOR	  =   0X00000001,	// 消防采集器
    DF_EXTRA_POWER_SEQUENCE   =   0X00000002, 	// 电源时序器
    DF_EXTRA_REMOTE_CONTROLER =   0X00000004, 	// 远程遥控器 
	DF_EXTRA_AUDIO_COLLECTOR  =   0x00000008, 	// 音频采集器（P030主板默认支持，无需启用）
	DF_EXTRA_AUDIO_MIXER      =   0x00000010, 	// 音频混音器
}Device_ExtraFeature;


typedef enum
{
	CHINESE=0,
	ENGLISH
}language_type_t;

#endif