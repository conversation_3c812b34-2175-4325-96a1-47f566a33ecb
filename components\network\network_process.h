/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:30:46 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 11:34:21
 */

#ifndef _NETWORK_PROCESS_H_
#define _NETWORK_PROCESS_H_

#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "lwip/ip_addr.h"

enum
{
	NET_TYPE_UNICAST_SERVER=1,
    NET_TYPE_UNICAST_PAGER=2,
	NET_TYPE_MULTICAST_SERVER=3,
	NET_TYPE_TCP_SERVER=4,
};

typedef struct
{
    unsigned char buf[MAX_BUF_SIZE];
    int len;
    unsigned char type; //1表示udp-sever,2表示udp-multicast,3表示tcp
}_stNetworkBuf;

typedef struct
{
    int write_pos;
    int read_pos;
    _stNetworkBuf stNetworkBuf[MAX_BUF_NUM];
}_stNetworkRecv;

extern _stNetworkRecv stNetworkRecv;

#define ip_addr_ismulticast(addr1) (((addr1)->s_addr & PP_HTONL(0xf0000000UL)) == PP_HTONL(0xe0000000UL))



void start_network_data_process_pthread(void);

void NetPkg_Add(int NetType,unsigned char *buf,unsigned int len);

unsigned char Calculate_XorDat(unsigned char *Data, int Length);    //异或和
void network_pkg_process(unsigned char NetPkgType,unsigned char *Pkg, int Pkg_Length);       //网络数据包处理


void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data);
void ProcessHostSetNetWork(unsigned char * pkg,int pkg_len,bool isMulticast);
void respond_null_payload_pkg(unsigned char *rxbuf);
void Set_zone_idle_status(unsigned char *rxbuf, const char *function,int line, bool canRepeat_paly);
void Host_Set_SerialNumber(unsigned char *pkg_data);
void Change_Device_Alias(unsigned char *rxbuf);
void requset_Host_Synchroniztion_Time(void);
void respond_pkg_time_synchronization(unsigned char *rxbuf, unsigned char code);
void pkg_set_local_time(unsigned char *rxbuf);
void pkg_query_current_status(unsigned char *rxbuf);
void respond_pkg_query_volume(unsigned char *rxbuf);
void pkg_set_volume(unsigned char *rxbuf);
void respond_pkg_query_sub_volume(unsigned int SetOrQuery,unsigned char *rxbuf);
void pkg_set_sub_volume(unsigned char *rxbuf,int payload_Length);
void pkg_control_volume_add_min(unsigned char *rxbuf);
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data);
void HOST_CONCENTRATED_PLAY_SOURCE(unsigned char *pkg_data);

void pkg_get_update_info(unsigned char *rxbuf);
int respond_pkg_update_status_code(unsigned char code);
int send_download_rate_of_progress(unsigned char progress);
void host_control_reboot(char *rxbuf,unsigned char netType);
void HOST_QUERY_SET_WORK_MODE(unsigned char *pkg_data);
#endif