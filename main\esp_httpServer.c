// ESP32 IDF HTTP Server Implementation
// Ported from mg_httpServer.c for ESP32 platform

#include <string.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <esp_http_server.h>
#include <esp_log.h>
#include <esp_system.h>
#include <esp_vfs.h>
#include <esp_spiffs.h>
#include <esp_ota_ops.h>
#include <esp_wifi.h>
#include <cJSON.h>
#include <mbedtls/base64.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>

#include "sysconf.h"
#include "const.h"
#include "esp_httpServer.h"

static const char *TAG = "ESP_HTTP_SERVER";

#define HTTP_POST_MAX_BODY 1400
#define MAX_SESSION_ID_LEN 33
#define MAX_PING_RESULTS_SIZE 512

// Global variables
static httpd_handle_t server = NULL;
static char g_login_token[MAX_SESSION_ID_LEN] = {0};

// Ping session structure for ESP32
typedef struct {
    TaskHandle_t pingTaskHandle;
    char results[MAX_PING_RESULTS_SIZE];
    int isCompleted;
    int64_t lastAccess;
    int isActive;
} PingSession;

static PingSession pingSession;
static SemaphoreHandle_t pingSessionMutex = NULL;

// CORS headers
static const char* cors_headers = 
    "Access-Control-Allow-Origin: *\r\n"
    "Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\r\n"
    "Access-Control-Allow-Headers: Content-Type, SESSION, Origin, Authorization\r\n"
    "Access-Control-Max-Age: 3600\r\n";

// Function prototypes
static esp_err_t handle_cors_options(httpd_req_t *req);
static esp_err_t handle_web_login(httpd_req_t *req);
static esp_err_t handle_web_api(httpd_req_t *req);
static esp_err_t handle_web_files(httpd_req_t *req);
static esp_err_t handle_ping_start(httpd_req_t *req);
static esp_err_t handle_ping_status(httpd_req_t *req);
static esp_err_t handle_ping_stop(httpd_req_t *req);
static esp_err_t handle_checklogin(httpd_req_t *req);
static void http_server_monitor_task(void *pvParameters);

// Initialize ping session
void init_ping_session() {
    if (pingSessionMutex == NULL) {
        pingSessionMutex = xSemaphoreCreateMutex();
    }
    xSemaphoreTake(pingSessionMutex, portMAX_DELAY);
    memset(&pingSession, 0, sizeof(PingSession));
    xSemaphoreGive(pingSessionMutex);
}

// Clear ping session
void clear_ping_session() {
    xSemaphoreTake(pingSessionMutex, portMAX_DELAY);
    if (pingSession.pingTaskHandle != NULL) {
        vTaskDelete(pingSession.pingTaskHandle);
    }
    memset(&pingSession, 0, sizeof(PingSession));
    xSemaphoreGive(pingSessionMutex);
}

// Generate session token
void generateToken(char *token) {
    const char *charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    int charset_len = strlen(charset);
    
    for (int i = 0; i < 32; i++) {
        token[i] = charset[esp_random() % charset_len];
    }
    token[32] = '\0';
}

// Extract session from HTTP headers
void extract_session(char *session, httpd_req_t *req) {
    size_t buf_len = httpd_req_get_hdr_value_len(req, "SESSION") + 1;
    if (buf_len > 1 && buf_len < MAX_SESSION_ID_LEN) {
        httpd_req_get_hdr_value_str(req, "SESSION", session, buf_len);
    }
}

// HTTP response helper functions
void http_web_response_normal(char *szBuf, int resultCode, char *msg) {
    cJSON *json = cJSON_CreateObject();
    cJSON *result = cJSON_CreateNumber(resultCode);
    cJSON_AddItemToObject(json, "result", result);
    
    if (msg != NULL) {
        cJSON *message = cJSON_CreateString(msg);
        cJSON_AddItemToObject(json, "message", message);
    }
    
    char *string = cJSON_Print(json);
    strcpy(szBuf, string);
    free(string);
    cJSON_Delete(json);
}

void http_web_response_login(char *szBuf, int resultCode, char *msg) {
    cJSON *json = cJSON_CreateObject();
    cJSON *result = cJSON_CreateNumber(resultCode);
    cJSON_AddItemToObject(json, "result", result);
    
    if (resultCode == HTTP_RESULT_OK) {
        cJSON *session = cJSON_CreateString(g_login_token);
        cJSON_AddItemToObject(json, "session", session);
    }
    
    if (msg != NULL) {
        cJSON *message = cJSON_CreateString(msg);
        cJSON_AddItemToObject(json, "message", message);
    }
    
    char *string = cJSON_Print(json);
    strcpy(szBuf, string);
    free(string);
    cJSON_Delete(json);
}

void http_web_response_get_deviceInfo(char *szBuf, int resultCode, char *msg) {
    cJSON *root = cJSON_CreateObject();

    if (msg != NULL)
    {
      cJSON_AddItemToObject(root, "msg", cJSON_CreateString(msg));
    }
    
    cJSON_AddItemToObject(root, "code", cJSON_CreateNumber(resultCode));

    char *deviceName=NULL;
    if(strlen(g_device_alias)>0)
    {
      deviceName=g_device_alias;
    }
    else
    {
      deviceName=g_ipAddress;
    }
    cJSON_AddItemToObject(root, "device_name", cJSON_CreateString(deviceName));
    cJSON_AddItemToObject(root, "device_model", cJSON_CreateNumber(CURRENT_DEVICE_MODEL));
    char str_mac[32]={0};
    MacArrayToString(str_mac,g_mac_addr);
    cJSON_AddItemToObject(root, "device_mac", cJSON_CreateString(str_mac));
    cJSON_AddItemToObject(root, "device_ip", cJSON_CreateString(g_ipAddress));
    cJSON_AddItemToObject(root, "device_network_mode", cJSON_CreateNumber(g_network_mode));
    cJSON_AddItemToObject(root, "server_connected", cJSON_CreateBool(IS_SERVER_CONNECTED));
    cJSON_AddItemToObject(root, "firmware_version", cJSON_CreateString(LOCAL_FIRMWARE_VERSION));
    

    char *jsonBuf = cJSON_Print(root);
    sprintf(szBuf, "%s", jsonBuf);
    free(jsonBuf);
    cJSON_Delete(root);
}

void http_web_response_get_networkInfo(char *szBuf, int resultCode, char *msg) {
    cJSON *json = cJSON_CreateObject();
    cJSON *result = cJSON_CreateNumber(resultCode);
    cJSON_AddItemToObject(json, "result", result);
    
    if (resultCode == HTTP_RESULT_OK) {
        cJSON *data = cJSON_CreateObject();
        
        // Get network information
        esp_netif_ip_info_t ip_info;
        esp_netif_t *netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
        if (netif != NULL) {
            esp_netif_get_ip_info(netif, &ip_info);
            
            char ip_str[16], netmask_str[16], gateway_str[16];
            esp_ip4addr_ntoa(&ip_info.ip, ip_str, sizeof(ip_str));
            esp_ip4addr_ntoa(&ip_info.netmask, netmask_str, sizeof(netmask_str));
            esp_ip4addr_ntoa(&ip_info.gw, gateway_str, sizeof(gateway_str));
            
            cJSON_AddStringToObject(data, "ip_address", ip_str);
            cJSON_AddStringToObject(data, "netmask", netmask_str);
            cJSON_AddStringToObject(data, "gateway", gateway_str);
        }
        
        cJSON_AddNumberToObject(data, "network_mode", g_network_mode);
        
        cJSON_AddItemToObject(json, "data", data);
    }
    
    char *string = cJSON_Print(json);
    strcpy(szBuf, string);
    free(string);
    cJSON_Delete(json);
}

// CORS OPTIONS handler
static esp_err_t handle_cors_options(httpd_req_t *req) {
    httpd_resp_set_status(req, "200 OK");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    httpd_resp_set_hdr(req, "Access-Control-Max-Age", "3600");
    httpd_resp_send(req, NULL, 0);
    return ESP_OK;
}

// Login handler
static esp_err_t handle_web_login(httpd_req_t *req) {
    char content[HTTP_POST_MAX_BODY];
    char jsonResponse[1024] = {0};
    
    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    
    if (req->method == HTTP_OPTIONS) {
        return handle_cors_options(req);
    }
    
    int ret = httpd_req_recv(req, content, MIN(req->content_len, sizeof(content)));
    if (ret <= 0) {
        if (ret == HTTPD_SOCK_ERR_TIMEOUT) {
            httpd_resp_send_408(req);
        }
        return ESP_FAIL;
    }
    
    content[ret] = '\0';
    
    cJSON *json = cJSON_Parse(content);
    if (json == NULL) {
        http_web_response_login(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }
    
    cJSON *jsUser = cJSON_GetObjectItem(json, "username");
    cJSON *jsPassword = cJSON_GetObjectItem(json, "password");
    
    if (jsUser && jsPassword && cJSON_IsString(jsUser) && cJSON_IsString(jsPassword)) {
        char *strUser = jsUser->valuestring;
        char *strPassword = jsPassword->valuestring;
        
        // Base64 decode password
        char base64de_password[128];
        size_t len;
        
        int decode_result = mbedtls_base64_decode((unsigned char*)base64de_password, 
                                                sizeof(base64de_password), &len,
                                                (unsigned char*)strPassword, strlen(strPassword));
        
        if (decode_result == 0) {
            base64de_password[len] = '\0';
            
            if (strcmp(strUser, "admin") == 0 && strcmp(base64de_password, "888888") == 0) {
                generateToken(g_login_token);
                http_web_response_login(jsonResponse, HTTP_RESULT_OK, NULL);
            } else {
                http_web_response_login(jsonResponse, HTTP_RESULT_ERROR_PASSWORD, NULL);
            }
        } else {
            http_web_response_login(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
        }
    } else {
        http_web_response_login(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
    }
    
    cJSON_Delete(json);
    
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
    
    return ESP_OK;
}

// General web API handler
static esp_err_t handle_web_api(httpd_req_t *req) {
    char content[HTTP_POST_MAX_BODY];
    char jsonResponse[1024] = {0};
    char session[128] = {0};
    ESP_LOGI(TAG, "Received request for1: %s", req->uri);
    
    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    
    if (req->method == HTTP_OPTIONS) {
        return handle_cors_options(req);
    }
    
    // Extract session
    extract_session(session, req);
    
    if (strcmp(session, g_login_token) != 0 || strlen(g_login_token) == 0) {
        http_web_response_normal(jsonResponse, HTTP_RESULT_SESSION_EXPIRED, NULL);
        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Received request for2: %s", req->uri);
    
    // Extract command from URI
    char *cmd = strstr(req->uri, "/web/");
    if (cmd != NULL) {
        cmd += 5; // Skip "/web/"
    } else {
        cmd = req->uri;
    }
    
    if (req->method == HTTP_GET) {
        // Handle GET requests
        ESP_LOGI(TAG, "GET request for command: %s", cmd);
        if(strcmp(cmd,"checklogin") == 0) {
            ESP_LOGI(TAG, "Processing checklogin request");
            http_web_response_normal(jsonResponse, HTTP_RESULT_OK, NULL);
        }
        if (strcmp(cmd, "deviceInfo") == 0) {
            http_web_response_get_deviceInfo(jsonResponse, HTTP_RESULT_OK, NULL);
        } else if (strcmp(cmd, "networkInfo") == 0) {
            http_web_response_get_networkInfo(jsonResponse, HTTP_RESULT_OK, NULL);
        } else {
            http_web_response_normal(jsonResponse, HTTP_RESULT_INVALID_COMMAND, NULL);
        }
    } else if (req->method == HTTP_POST) {
        // Handle POST requests
        int ret = httpd_req_recv(req, content, MIN(req->content_len, sizeof(content)));
        if (ret <= 0) {
            if (ret == HTTPD_SOCK_ERR_TIMEOUT) {
                httpd_resp_send_408(req);
            }
            return ESP_FAIL;
        }
        content[ret] = '\0';
        
        cJSON *json = cJSON_Parse(content);
        if (json == NULL) {
            http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
        } else {
            if (strcmp(cmd, "terminalInfo") == 0) {
                // Handle terminal info update
                cJSON *jsDevName = cJSON_GetObjectItem(json, "dev_name");
                cJSON *jsAuxVolume = cJSON_GetObjectItem(json, "aux_volume");
                
                bool bUpdate = false;
                if (jsDevName && cJSON_IsString(jsDevName)) {
                    int valueLen = strlen(jsDevName->valuestring);
                    snprintf(g_device_alias, valueLen > 32 ? 33 : valueLen + 1, "%s", jsDevName->valuestring);
                    save_sysconf(INI_SECTION_DEVICE, "Device_Name");
                    bUpdate = true;
                }
                
                if (jsAuxVolume && cJSON_IsNumber(jsAuxVolume)) {
                    g_aux_volume = jsAuxVolume->valueint;
                    if (g_aux_volume >= 0 && g_aux_volume <= 100) {
                        save_sysconf(INI_SECTION_BASIC, "Aux_Volume");
                        bUpdate = true;
                    }
                }
                
                if (bUpdate) {
                    http_web_response_normal(jsonResponse, HTTP_RESULT_OK, NULL);
                } else {
                    http_web_response_normal(jsonResponse, HTTP_RESULT_PARM_ERROR, NULL);
                }
            } else if (strcmp(cmd, "reboot") == 0) {
                http_web_response_normal(jsonResponse, HTTP_RESULT_OK, NULL);
                // Send response first, then reboot
                httpd_resp_set_type(req, "application/json");
                httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
                
                vTaskDelay(pdMS_TO_TICKS(300));
                esp_restart();
                return ESP_OK;
            } else {
                http_web_response_normal(jsonResponse, HTTP_RESULT_INVALID_COMMAND, NULL);
            }
            cJSON_Delete(json);
        }
    }
    
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, jsonResponse, strlen(jsonResponse));
    
    return ESP_OK;
}

// Static file handler
static esp_err_t handle_web_files(httpd_req_t *req) {
    char filepath[256];
    
    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    
    if (req->method == HTTP_OPTIONS) {
        return handle_cors_options(req);
    }
    
    // Default to index.html for root
    if (strcmp(req->uri, "/") == 0) {
        strcpy(filepath, "/spiffs/index.html");
    } else {
        //snprintf(filepath, sizeof(filepath), "/spiffs%s", req->uri);
        strlcpy(filepath, "/spiffs", sizeof(filepath));
        strlcat(filepath, req->uri, sizeof(filepath));
    }
    
    // Check if file exists
    struct stat file_stat;
    if (stat(filepath, &file_stat) == -1) {
        // File not found, serve index.html as fallback for SPA
        strcpy(filepath, "/spiffs/index.html");
        if (stat(filepath, &file_stat) == -1) {
            httpd_resp_send_404(req);
            return ESP_FAIL;
        }
    }
    
    FILE *fd = fopen(filepath, "r");
    if (!fd) {
        httpd_resp_send_404(req);
        return ESP_FAIL;
    }
    
    // Set content type based on file extension
    const char *ext = strrchr(filepath, '.');
    if (ext) {
        if (strcmp(ext, ".html") == 0) {
            httpd_resp_set_type(req, "text/html");
        } else if (strcmp(ext, ".css") == 0) {
            httpd_resp_set_type(req, "text/css");
        } else if (strcmp(ext, ".js") == 0) {
            httpd_resp_set_type(req, "application/javascript");
        } else if (strcmp(ext, ".png") == 0) {
            httpd_resp_set_type(req, "image/png");
        } else if (strcmp(ext, ".jpg") == 0 || strcmp(ext, ".jpeg") == 0) {
            httpd_resp_set_type(req, "image/jpeg");
        }
    }
    
    char *chunk = malloc(1024);
    if (chunk == NULL) {
        fclose(fd);
        httpd_resp_send_500(req);
        return ESP_FAIL;
    }
    
    size_t chunksize;
    do {
        chunksize = fread(chunk, 1, 1024, fd);
        if (chunksize > 0) {
            if (httpd_resp_send_chunk(req, chunk, chunksize) != ESP_OK) {
                fclose(fd);
                free(chunk);
                return ESP_FAIL;
            }
        }
    } while (chunksize != 0);
    
    httpd_resp_send_chunk(req, NULL, 0);
    fclose(fd);
    free(chunk);
    
    return ESP_OK;
}

// HTTP server configuration
static httpd_config_t server_config = HTTPD_DEFAULT_CONFIG();

// URI handlers
static const httpd_uri_t login_uri = {
    .uri       = "/web/login",
    .method    = HTTP_POST,
    .handler   = handle_web_login,
    .user_ctx  = NULL
};

static const httpd_uri_t api_uri = {
    .uri       = "/web/*",
    .method    = HTTP_POST,
    .handler   = handle_web_api,
    .user_ctx  = NULL
};

static const httpd_uri_t api_get_uri = {
    .uri       = "/web/*",
    .method    = HTTP_GET,
    .handler   = handle_web_api,
    .user_ctx  = NULL
};

static const httpd_uri_t checklogin_uri = {
    .uri       = "/web/checklogin",
    .method    = HTTP_GET,
    .handler   = handle_checklogin,
    .user_ctx  = NULL
};

static const httpd_uri_t checklogin_options_uri = {
    .uri       = "/web/checklogin",
    .method    = HTTP_OPTIONS,
    .handler   = handle_checklogin,
    .user_ctx  = NULL
};

// Simple checklogin handler
static esp_err_t handle_checklogin(httpd_req_t *req) {
    char jsonResponse[256] = {0};

    ESP_LOGI(TAG, "Checklogin request received from: %s, method: %d", req->uri, req->method);

    // Add CORS headers
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Headers", "Content-Type, SESSION, Origin, Authorization");
    httpd_resp_set_hdr(req, "Access-Control-Max-Age", "3600");

    // Handle OPTIONS request (CORS preflight)
    if (req->method == HTTP_OPTIONS) {
        ESP_LOGI(TAG, "Handling OPTIONS request for checklogin");
        httpd_resp_set_status(req, "200 OK");
        httpd_resp_send(req, NULL, 0);
        return ESP_OK;
    }

    // Handle GET request
    if (req->method == HTTP_GET) {
        ESP_LOGI(TAG, "Handling GET request for checklogin");
        // Simple success response
        http_web_response_normal(jsonResponse, HTTP_RESULT_OK, NULL);

        httpd_resp_set_type(req, "application/json");
        httpd_resp_send(req, jsonResponse, strlen(jsonResponse));

        ESP_LOGI(TAG, "Checklogin response sent successfully");
        return ESP_OK;
    }

    // Method not allowed
    ESP_LOGW(TAG, "Method %d not allowed for checklogin", req->method);
    httpd_resp_send_err(req, HTTPD_405_METHOD_NOT_ALLOWED, "Method not allowed");
    return ESP_FAIL;
}

// HTTP server monitoring task
static void http_server_monitor_task(void *pvParameters) {
    httpd_handle_t *server_handle = (httpd_handle_t *)pvParameters;

    while (1) {
        if (*server_handle) {
            // Log server status periodically
            ESP_LOGD(TAG, "HTTP Server is running, monitoring connections...");
        }
        vTaskDelay(pdMS_TO_TICKS(30000)); // Check every 30 seconds
    }
}

// Start HTTP server
esp_err_t start_esp_httpServer(void) {
    init_ping_session();

    server_config.lru_purge_enable = true;
    server_config.max_uri_handlers = 16;
    server_config.max_open_sockets = 4;  // Increase max concurrent connections
    server_config.stack_size = 8192;
    server_config.server_port = 8080;
    server_config.backlog_conn = 5;       // Queue up to 5 pending connections
    server_config.recv_wait_timeout = 5;  // 5 second receive timeout
    server_config.send_wait_timeout = 5;  // 5 second send timeout

    ESP_LOGI(TAG, "Starting HTTP Server on port: '%d' with max_open_sockets: %d",
             server_config.server_port, server_config.max_open_sockets);
    
    if (httpd_start(&server, &server_config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");
        
        // Register handlers in order of specificity (most specific first)
        httpd_register_uri_handler(server, &login_uri);
        httpd_register_uri_handler(server, &checklogin_uri);
        httpd_register_uri_handler(server, &checklogin_options_uri);
        httpd_register_uri_handler(server, &api_uri);
        httpd_register_uri_handler(server, &api_get_uri);

        // Start monitoring task
        xTaskCreate(http_server_monitor_task, "http_monitor", 2048, &server, 3, NULL);

        ESP_LOGI(TAG, "HTTP Server started successfully with monitoring");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Error starting HTTP server!");
    return ESP_FAIL;
}

// Stop HTTP server
esp_err_t stop_esp_httpServer(void) {
    if (server) {
        clear_ping_session();
        if (pingSessionMutex) {
            vSemaphoreDelete(pingSessionMutex);
            pingSessionMutex = NULL;
        }
        return httpd_stop(server);
    }
    return ESP_OK;
}

// Task wrapper for FreeRTOS
void esp_httpServer_task(void *pvParameters) {
    start_esp_httpServer();
    
    // Keep task running
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

// Start HTTP server task
void start_esp_httpServer_task(void) {
    xTaskCreate(esp_httpServer_task, "esp_http_server", 8192, NULL, 5, NULL);
    ESP_LOGI(TAG, "ESP HTTP Server task created");
} 