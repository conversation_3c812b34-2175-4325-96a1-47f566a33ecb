#ifndef ESP_HTTP_SERVER_H
#define ESP_HTTP_SERVER_H

#include <esp_err.h>

#ifdef __cplusplus
extern "C" {
#endif


// HTTP Result codes (same as original)
enum{
    HTTP_RESULT_INVALID_COMMAND=-2,     //非法指令
    HTTP_RESULT_PARM_ERROR=-1,          //参数错误
    HTTP_RESULT_OK=0,                   //正常请求
    HTTP_RESULT_FAILED=1,              //请求失败
    HTTP_RESULT_CHANGE_NETWORK_MODE_OK=100,    //修改网络模式成功
    HTTP_RESULT_CHANGE_NETWORK_IP_OK=101,      //修改网络IP成功
    HTTP_RESULT_CHANGE_NETWORK_IP_MODE_OK=102, //同时修改网络IP和网络模式成功
    HTTP_RESULT_NOT_CHANGE_NETWORK_IP_MODE=103, //未修改网络IP和网络模式
    HTTP_RESULT_ERROR_PASSWORD=998,    // 密码错误
    HTTP_RESULT_SESSION_EXPIRED=999,    //会话过期
};

/**
 * @brief 启动ESP32 HTTP服务器
 * 
 * @return esp_err_t ESP_OK on success, ESP_FAIL on error
 */
esp_err_t start_esp_httpServer(void);

/**
 * @brief 停止ESP32 HTTP服务器
 * 
 * @return esp_err_t ESP_OK on success
 */
esp_err_t stop_esp_httpServer(void);

/**
 * @brief 启动ESP32 HTTP服务器任务
 * 
 * 创建一个FreeRTOS任务来运行HTTP服务器
 */
void start_esp_httpServer_task(void);

/**
 * @brief HTTP服务器任务函数
 * 
 * @param pvParameters 任务参数（未使用）
 */
void esp_httpServer_task(void *pvParameters);

/**
 * @brief 初始化ping会话
 */
void init_ping_session(void);

/**
 * @brief 清理ping会话
 */
void clear_ping_session(void);

/**
 * @brief 生成会话令牌
 * 
 * @param token 输出令牌字符串（至少33字节）
 */
void generateToken(char *token);

/**
 * @brief HTTP响应-普通响应
 * 
 * @param szBuf 输出缓冲区
 * @param resultCode 结果代码
 * @param msg 消息字符串（可选）
 */
void http_web_response_normal(char *szBuf, int resultCode, char *msg);

/**
 * @brief HTTP响应-登录响应
 * 
 * @param szBuf 输出缓冲区
 * @param resultCode 结果代码
 * @param msg 消息字符串（可选）
 */
void http_web_response_login(char *szBuf, int resultCode, char *msg);

/**
 * @brief HTTP响应-设备信息响应
 * 
 * @param szBuf 输出缓冲区
 * @param resultCode 结果代码
 * @param msg 消息字符串（可选）
 */
void http_web_response_get_deviceInfo(char *szBuf, int resultCode, char *msg);

/**
 * @brief HTTP响应-网络信息响应
 * 
 * @param szBuf 输出缓冲区
 * @param resultCode 结果代码
 * @param msg 消息字符串（可选）
 */
void http_web_response_get_networkInfo(char *szBuf, int resultCode, char *msg);

#ifdef __cplusplus
}
#endif

#endif // ESP_HTTP_SERVER_H 