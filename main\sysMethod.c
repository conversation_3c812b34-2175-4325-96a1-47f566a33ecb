#include "esp_mac.h"
#include "string.h"
#include "stdlib.h"
#include "mbedtls/md5.h"
#include "esp_system.h"
#include "esp_heap_caps.h"
#include "sysMethod.h"

uint64_t  get_esp32_uuid() {
    uint8_t mac[6];
    esp_efuse_mac_get_default(mac);
    return ((uint64_t)mac[0] << 40) | ((uint64_t)mac[1] << 32) |
           ((uint64_t)mac[2] << 24) | ((uint64_t)mac[3] << 16) |
           ((uint64_t)mac[4] << 8) | mac[5];
}

void print_free_memory() {
    printf("Free heap (internal): %d bytes\n", heap_caps_get_free_size(MALLOC_CAP_INTERNAL));
    printf("Free heap (default): %d bytes\n", heap_caps_get_free_size(MALLOC_CAP_DEFAULT));
    printf("Minimum free heap ever: %d bytes\n", heap_caps_get_minimum_free_size(MALLOC_CAP_DEFAULT));
}


void calculate_md5(const char *input_str, char *output_str) {
    // 创建 MD5 上下文
    mbedtls_md5_context ctx;
    mbedtls_md5_init(&ctx);

    // 启动 MD5 计算
    mbedtls_md5_starts(&ctx);

    // 更新 MD5 计算，传入输入字符串
    mbedtls_md5_update(&ctx, (const unsigned char *)input_str, strlen(input_str));

    // 完成 MD5 计算，得到哈希值
    unsigned char hash[16];
    mbedtls_md5_finish(&ctx, hash);

    // 清理上下文
    mbedtls_md5_free(&ctx);

    // 将哈希值转换为十六进制字符串
    for (int i = 0; i < 16; i++) {
        sprintf(output_str + i * 2, "%02x", hash[i]);
    }
}

void sysRestart()
{
    esp_restart();
}

void System_Reboot()
{
    printf("System_Reboot\n");
    //todo关闭功放和信号
    esp_restart();
}


//MAC字节数组转换为标准字符串
void MacArrayToString(char* szMac, const unsigned char arrayMac[])
{
    sprintf(szMac, "%02x:%02x:%02x:%02x:%02x:%02x",arrayMac[0],arrayMac[1],arrayMac[2],arrayMac[3],arrayMac[4],arrayMac[5]);
}