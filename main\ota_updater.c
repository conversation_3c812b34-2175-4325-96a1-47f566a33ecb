#include "esp_https_ota.h"
#include "esp_log.h"
#include "sysconf.h"
#include "network.h"
#include "ota_updater.h"

static const char *TAG = "OTA";

bool isUpdateing = false;

esp_err_t ota_init() {
    // 初始化必要的OTA配置
    return ESP_OK;
}

void ota_start(void *url) {
    char *ota_url = (char *)url;
    esp_err_t ret = ESP_OK;
    esp_http_client_config_t config = {
        .url = ota_url,
        .auth_type = HTTP_AUTH_TYPE_NONE,
        .method = HTTP_METHOD_GET,
        .cert_pem = NULL,  // 根据服务器证书情况配置
        .skip_cert_common_name_check = true,  // 添加此参数跳过验证
        .timeout_ms = 30000
    };
    
    esp_https_ota_config_t ota_config = {
        .http_config = &config,
    };

    esp_https_ota_handle_t https_ota_handle = NULL;
    ESP_LOGI(TAG, "Starting OTA update from: %s", ota_url);

    esp_err_t ota_finish_err = ESP_OK;
    ret = esp_https_ota_begin(&ota_config, &https_ota_handle);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ESP HTTPS OTA Begin failed");
        goto ota_end;
    }

    esp_app_desc_t app_desc;
    ret = esp_https_ota_get_img_desc(https_ota_handle, &app_desc);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "esp_https_ota_get_img_desc failed");
        goto ota_end;
    }

    // 获取固件总大小用于计算进度
    int total_size = esp_https_ota_get_image_size(https_ota_handle);
    //如果总大小大于0x1C0000，跳出
    if(total_size > 0x1C0000)
    {
        ESP_LOGE(TAG, "OTA file size too large: %d", total_size);
        goto ota_end;
    }
    int downloaded_size = 0;
    int last_progress = 0;

    while (1) {
        ret = esp_https_ota_perform(https_ota_handle);
        if (ret != ESP_ERR_HTTPS_OTA_IN_PROGRESS) {
            break;
        }

        // 获取当前已下载的大小
        downloaded_size = esp_https_ota_get_image_len_read(https_ota_handle);

        // 计算下载进度百分比
        int current_progress = 0;
        if (total_size > 0) {
            current_progress = (downloaded_size * 100) / total_size;
            // 确保进度在1-99之间，100%留给完成时发送
            if (current_progress < 1) current_progress = 1;
            if (current_progress > 99) current_progress = 99;
        }

        // 每增加5%或10%发送一次进度更新，避免过于频繁
        if (current_progress >= last_progress + 5) {
            send_download_rate_of_progress(current_progress);
            last_progress = current_progress;
            ESP_LOGI(TAG, "OTA progress: %d%% (%d/%d bytes)", current_progress, downloaded_size, total_size);
        }
    }

    if (ret == ESP_OK) {
        // 发送100%完成进度
        send_download_rate_of_progress(100);
        ESP_LOGI(TAG, "OTA update successful. Restarting...");
        ota_finish_err = esp_https_ota_finish(https_ota_handle);
        if (ota_finish_err == ESP_OK) {
            respond_pkg_update_status_code(UPDATE_SUCCEED);
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            esp_restart();
        }
    } else {
        ESP_LOGE(TAG, "OTA failed: 0x%x", ret);
    }

ota_end:
    esp_https_ota_abort(https_ota_handle);
    free(ota_url);
    isUpdateing=false;
    respond_pkg_update_status_code(UPDATE_FAIL);
    vTaskDelete(NULL);
}

void start_ota_task(char *url) {
    // 从参数中获取URL
    if(isUpdateing)
    {
        ESP_LOGI(TAG, "OTA is already running");
        return;
    }
    isUpdateing=true;
    respond_pkg_update_status_code(START_DOWNLOAD);
    char *url_copy = strdup(url);
    xTaskCreate(ota_start, "ota task", 4096, url_copy, 3, NULL);
}
