# ESP32 HTTP Server CMakeLists.txt Example
# 这是一个示例文件，展示如何在main/CMakeLists.txt中添加ESP32 HTTP服务器

# 在原有的main/CMakeLists.txt中，需要添加以下内容：

# 1. 添加源文件
set(COMPONENT_SRCS
    "main.c"
    "esp_httpServer.c"     # 新增的ESP32 HTTP服务器文件
    # ... 其他已有的源文件
)

# 2. 添加头文件目录
set(COMPONENT_ADD_INCLUDEDIRS ".")

# 3. 添加依赖的组件
set(COMPONENT_REQUIRES 
    driver
    esp_wifi
    esp_http_server        # ESP32 HTTP服务器组件
    esp_netif
    esp_event
    json                   # cJSON组件
    mbedtls               # Base64解码
    spiffs                # 文件系统（如果使用SPIFFS存储网页文件）
    fatfs                 # 或者使用FAT文件系统
    app_update            # OTA更新
    # ... 其他依赖组件
)

# 4. 注册组件
register_component()

# 5. 可选：添加编译选项
target_compile_options(${COMPONENT_LIB} PRIVATE -Wno-unused-function)

# 6. 可选：链接静态库
# target_link_libraries(${COMPONENT_LIB} some_static_lib)

# 注意事项：
# 1. 确保在sdkconfig中启用了HTTP服务器组件：
#    CONFIG_HTTPD_MAX_REQ_HDR_LEN=1024
#    CONFIG_HTTPD_MAX_URI_LEN=512
#    CONFIG_HTTPD_ERR_RESP_NO_DELAY=y
#    CONFIG_HTTPD_PURGE_BUF_LEN=32
#
# 2. 如果使用SPIFFS存储网页文件，需要在partition.csv中添加SPIFFS分区：
#    spiffs, data, spiffs, 0x200000, 0x100000,
#
# 3. 确保有足够的堆内存用于HTTP服务器操作
#
# 4. 在main.c中调用start_esp_httpServer_task()来启动HTTP服务器 