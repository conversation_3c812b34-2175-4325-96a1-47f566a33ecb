/* Play an OPUS file from HTTP

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "nvs_flash.h"
#include "sdkconfig.h"
#include "audio_element.h"
#include "audio_pipeline.h"
#include "audio_event_iface.h"
#include "audio_common.h"
#include "http_stream.h"
#include "i2s_stream.h"
#include "opus_decoder.h"
#include "raw_opus_decoder.h"

#include "esp_peripherals.h"
#include "periph_wifi.h"
#include "board.h"

#include "audio.h"
#include "netHandle.h"
#include "sysconf.h"

static const char *TAG = "OPUS_PLAYER";

// 正确的读取回调实现
static int audio_read_callback(audio_element_handle_t el, char *buffer, int len, TickType_t timeout, void *ctx) {
    // 从你的数据源（如网络/文件）读取数据
    int bytes_read = rb_read(audio_data_rb,buffer, len, 0xFFFFFFFF);
    //ESP_LOGI(TAG, "bytes_read=%d",bytes_read);
    // 处理数据不足的情况（补静音）
    if (bytes_read < len) {
        memset(buffer + bytes_read, 0, len - bytes_read);
        ESP_LOGW(TAG, "Underrun: %d bytes missing", len - bytes_read);
    }
    return len;
}


static int i2s_write_cb(audio_element_handle_t el, char *buf, int len, TickType_t wait_time, void *ctx)
{
    stream_func i2s_write_func = (stream_func)ctx;
    size_t size = len;

    float volumeGain = 100/100.0; // 音量调整因子，0.0到1.0之间
    // 16位PCM数据（假设解码器输出为16位有符号）
    int16_t *pcm = (int16_t *)buf;
    int samples = len / sizeof(int16_t);
    
    for(int i = 0; i < samples; i++) {
        float sample = pcm[i] * volumeGain;
        // 限制幅度在16位范围内
        pcm[i] = (int16_t)(sample > 32767 ? 32767 : (sample < -32768 ? -32768 : sample));
    }
    
    int ret = i2s_write_func(el, buf, size, wait_time, ctx);
    if (ret < 0) {
        ESP_LOGE(TAG, "i2s write failed");
    }
    return len;
}

void opus_play_stream(void *arg)
{
    if(g_playTaskSrc != TASK_SRC_SOUNDCARD)
    {
        vTaskDelete(NULL);
    }

    //初始化ringbuf
    ringbuf_init(RINGBUF_OPUS_BLOCK_SIZE, RINGBUF_OPUS_BLOCKS_NUM);


    audio_element_handle_t  i2s_stream_writer, opus_decoder;

    ESP_LOGI(TAG, "[2.0] Create audio pipeline for playback");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    audio_pipeline_handle_t pipeline = audio_pipeline_init(&pipeline_cfg);
    mem_assert(pipeline);

    ESP_LOGI(TAG, "[2.2] Create i2s stream to write data to codec chip");
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT();
    i2s_cfg.type = AUDIO_STREAM_WRITER;
#if 1
    i2s_cfg.chan_cfg.dma_frame_num = 960;   //896/44100=20ms
    i2s_cfg.chan_cfg.dma_desc_num = 8;      //8*20ms=160ms
#endif
    i2s_stream_writer = i2s_stream_init(&i2s_cfg);

    //audio_element_set_input_timeout(i2s_stream_writer, 20000 / portTICK_PERIOD_MS);

    audio_element_set_write_cb(i2s_stream_writer, i2s_write_cb, audio_element_get_write_cb(i2s_stream_writer));

    i2s_stream_set_clk(i2s_stream_writer, 48000, 16, 1);

    ESP_LOGI(TAG, "[2.3] Create opus decoder to decode opus file");
    #if 0
    opus_decoder_cfg_t opus_cfg = DEFAULT_OPUS_DECODER_CONFIG();
    opus_decoder = decoder_opus_init(&opus_cfg);
    audio_element_set_read_cb(opus_decoder, audio_read_callback, NULL);
    #endif
    raw_opus_dec_cfg_t opus_dec_cfg = RAW_OPUS_DEC_CONFIG_DEFAULT();
    opus_dec_cfg.enable_frame_length_prefix = false;
    opus_dec_cfg.sample_rate = 48000;
    opus_dec_cfg.channels = 1;
    opus_dec_cfg.dec_frame_size = 320;
    opus_dec_cfg.task_stack = 15*1024;
    opus_decoder = raw_opus_decoder_init(&opus_dec_cfg);
    audio_element_set_read_cb(opus_decoder, audio_read_callback, NULL);


    ESP_LOGI(TAG, "[2.4] Register all elements to audio pipeline");
    audio_pipeline_register(pipeline, opus_decoder,        "opus");
    audio_pipeline_register(pipeline, i2s_stream_writer,  "i2s");

    ESP_LOGI(TAG, "[2.5] Link it together source-->opus_decoder-->i2s_stream");

    const char *link_tag[] = {"opus", "i2s"};
    audio_pipeline_link(pipeline, &link_tag[0], 2);
    
    // 添加事件监听
    ESP_LOGI(TAG, "[ 4 ] Set up event listener");
    audio_event_iface_cfg_t evt_cfg = AUDIO_EVENT_IFACE_DEFAULT_CFG();
    audio_event_iface_handle_t evt = audio_event_iface_init(&evt_cfg);
    audio_pipeline_set_listener(pipeline, evt);

    bool pipeline_runing = false;
    while(g_playTaskSrc == TASK_SRC_SOUNDCARD)
    {
        //说明pipeline已经开始运行了
        if(g_stream_ready_play)
        {
            if(!pipeline_runing)
            {
                ESP_LOGI(TAG, "start pipeline!");
                audio_pipeline_run(pipeline);
                pipeline_runing=true;
            }
        }

        // 事件处理部分新增
        audio_event_iface_msg_t msg;
        if (audio_event_iface_listen(evt, &msg, 0) == ESP_OK) {
            /* 当收到停止事件时 */
            if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT 
                && (msg.source == (void *)i2s_stream_writer || msg.source == (void *)opus_decoder)
                && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
                && ((int)msg.data == AEL_STATUS_STATE_STOPPED || (int)msg.data == AEL_STATUS_STATE_FINISHED)) {
                ESP_LOGW(TAG, "KK[ * ] %s stopped", (msg.source == i2s_stream_writer) ? "I2S" : "OPUS_DECODER");
                break;
            }
        }
        vTaskDelay(10 / portTICK_PERIOD_MS);
    }
    

    ESP_LOGI(TAG, "Stop audio_pipeline");
    // 销毁事件接口
    audio_event_iface_destroy(evt);
    audio_pipeline_stop(pipeline);
    #if 1   //一定要加rb_done_write，否则可能因为audio_read_callback阻塞audio_data_rb，无限等待,放到ringbuf_deinit中也行
    if(audio_data_rb)
        rb_done_write(audio_data_rb);
    #endif
    audio_pipeline_wait_for_stop(pipeline);
    audio_pipeline_terminate(pipeline);
    /* Terminate the pipeline before removing the listener */
    audio_pipeline_unregister(pipeline, i2s_stream_writer);
    audio_pipeline_unregister(pipeline, opus_decoder);

    audio_pipeline_remove_listener(pipeline);

    /* Release all resources */
    audio_pipeline_deinit(pipeline);
    audio_element_deinit(i2s_stream_writer);
    audio_element_deinit(opus_decoder);

    //释放ringbuf
    ringbuf_deinit();

    ESP_LOGI(TAG, "opus_play_stream END...");

    vTaskDelete(NULL);
}



void start_opus_stream_task() {
    xTaskCreate(opus_play_stream, "opus_player_stream", 4096, NULL, 10, NULL); 
}
