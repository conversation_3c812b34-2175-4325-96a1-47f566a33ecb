#include <stdio.h>
#include <string.h>
#include "driver/spi_master.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_netif.h"
#include "esp_eth.h"
#include "esp_event.h"
//#include "ethernet_init.h"
#include "sdkconfig.h"

#include "ch390d_driver.h"

#include "sysconf.h"


static const char *TAG = "ETH_DRIVER";

esp_eth_handle_t ch390d_master_eth_handle = NULL;


/** Event handler for Ethernet events */
static void eth_event_handler(void *arg, esp_event_base_t event_base,
                                int32_t event_id, void *event_data)
{
    #if 0
    uint8_t mac_addr[6] = {0};
    /* we can get the ethernet driver handle from event data */
    esp_eth_handle_t eth_handle = *(esp_eth_handle_t *)event_data;
    #endif
    switch (event_id) {
        case ETHERNET_EVENT_CONNECTED:
            ESP_LOGI(TAG, "Ethernet Link Up");
            eth_link_status=1;
            #if 0
            esp_eth_ioctl(eth_handle, ETH_CMD_G_MAC_ADDR, mac_addr);
            ESP_LOGI(TAG, "Ethernet HW Addr %02x:%02x:%02x:%02x:%02x:%02x",
            mac_addr[0], mac_addr[1], mac_addr[2], mac_addr[3], mac_addr[4], mac_addr[5]);
            #endif
        break;
        case ETHERNET_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "Ethernet Link Down");
            eth_link_status=0;
            #if 0
            esp_eth_ioctl(eth_handle, ETH_CMD_G_MAC_ADDR, mac_addr);
            ESP_LOGI(TAG, "Ethernet HW Addr %02x:%02x:%02x:%02x:%02x:%02x",\
                mac_addr[0], mac_addr[1], mac_addr[2], mac_addr[3], mac_addr[4], mac_addr[5]);
            #endif
        break;
        case ETHERNET_EVENT_START:
            ESP_LOGI(TAG, "Ethernet Started");
        break;
        case ETHERNET_EVENT_STOP:
            ESP_LOGI(TAG, "Ethernet Stopped");
        break;
        default:
        break;
    }
}

/** Event handler for IP_EVENT_ETH_GOT_IP */
static void got_ip_event_handler(void *arg, esp_event_base_t event_base,
       int32_t event_id, void *event_data)
{
    ip_event_got_ip_t *event = (ip_event_got_ip_t *) event_data;
    const esp_netif_ip_info_t *ip_info = &event->ip_info;

    ESP_LOGI(TAG, "Ethernet Got IP Address");
    ESP_LOGI(TAG, "ETHIP:" IPSTR, IP2STR(&ip_info->ip));
    ESP_LOGI(TAG, "ETHMASK:" IPSTR, IP2STR(&ip_info->netmask));
    ESP_LOGI(TAG, "ETHGW:" IPSTR, IP2STR(&ip_info->gw));

    network_init_flag=1;
    sprintf(g_ipAddress, IPSTR, IP2STR(&ip_info->ip));
    sprintf(g_subnet_mask, IPSTR, IP2STR(&ip_info->netmask));
    sprintf(g_gateway, IPSTR, IP2STR(&ip_info->gw));
}


void set_network_mode_static(bool use_static_ip)
{
    esp_netif_t *netif = esp_netif_get_handle_from_ifkey("ETH_DEF");
    if(netif) {
        if(use_static_ip) {
            esp_netif_dhcpc_stop(netif);
            esp_netif_ip_info_t static_ip_config;

            // 将字符串转换为IP地址
            ip4addr_aton(g_Static_ip_address, &static_ip_config.ip);
            ip4addr_aton(g_Subnet_Mask, &static_ip_config.netmask);
            ip4addr_aton(g_GateWay, &static_ip_config.gw);

            esp_netif_set_ip_info(netif, &static_ip_config);
        } else {
            esp_netif_dhcpc_stop(netif);
            esp_netif_set_ip_info(netif, NULL); // 清除静态IP
            esp_netif_dhcpc_start(netif); // 启动DHCP
        }
    }
}

void eth_ch390d_master_driver_init(void)
{
    esp_netif_config_t cfg = ESP_NETIF_DEFAULT_ETH();
    esp_netif_t *eth_netif = esp_netif_new(&cfg);
    
    //DHCP也可以不需要set_network_mode_static，因为DHCP默认启动
    if(g_IP_Assign == IP_ASSIGN_STATIC)
    {
        set_network_mode_static(true);
    }
    else
    {
        set_network_mode_static(false);
    }

    spi_bus_config_t buscfg = {
        .mosi_io_num = ETH_MASTER_MOSI_GPIO,
        .miso_io_num = ETH_MASTER_MISO_GPIO,
        .sclk_io_num = ETH_MASTER_SCLK_GPIO,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
    };
    ESP_ERROR_CHECK(spi_bus_initialize(SPI_MASTER_HOST, &buscfg, SPI_DMA_CH_AUTO));

    spi_device_interface_config_t spi_devcfg = {
        .mode = 0,
        .clock_speed_hz = SPI_MASTER_CLOCK_MHZ * 1000 * 1000,
        .spics_io_num = ETH_MASTER_CS_GPIO,
        .queue_size = 20,
    };

    eth_ch390_config_t ch390_config = ETH_CH390_DEFAULT_CONFIG(SPI_MASTER_HOST, &spi_devcfg);
    
    #if 1
    ch390_config.int_gpio_num = ETH_MASTER_INT_GPIO;
    #else
    ch390_config.int_gpio_num = -1;
    ch390_config.poll_period_ms = 20;
    #endif

    eth_mac_config_t mac_config = ETH_MAC_DEFAULT_CONFIG();
    mac_config.rx_task_stack_size = 4096;
    mac_config.rx_task_prio = configMAX_PRIORITIES - 1;  // 设置优先级为最高
    esp_eth_mac_t *mac = esp_eth_mac_new_ch390(&ch390_config, &mac_config);

    eth_phy_config_t phy_config = ETH_PHY_DEFAULT_CONFIG();
    phy_config.autonego_timeout_ms = 0; // ENC28J60 doesn't support auto-negotiation
    phy_config.reset_gpio_num = -1; // ENC28J60 doesn't have a pin to reset internal PHY
    esp_eth_phy_t *phy = esp_eth_phy_new_ch390(&phy_config);

    esp_eth_config_t eth_config = ETH_DEFAULT_CONFIG(mac, phy);

    ESP_ERROR_CHECK(esp_eth_driver_install(&eth_config, &ch390d_master_eth_handle));

    /* attach Ethernet driver to TCP/IP stack */
    ESP_ERROR_CHECK(esp_netif_attach(eth_netif, esp_eth_new_netif_glue(ch390d_master_eth_handle)));
}


void ch390d_driver_init(void)
{
    // Initialize TCP/IP network interface aka the esp-netif (should be called only once in application)
    ESP_ERROR_CHECK(esp_netif_init());
    // Create default event loop that running in background
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    eth_ch390d_master_driver_init();

    esp_event_handler_register(ETH_EVENT, ESP_EVENT_ANY_ID, &eth_event_handler, NULL);
    esp_event_handler_register(IP_EVENT, IP_EVENT_ETH_GOT_IP, &got_ip_event_handler, NULL);


    ESP_ERROR_CHECK(esp_eth_start(ch390d_master_eth_handle));

#if 1
    //获取网卡的ADDR
    esp_eth_ioctl(ch390d_master_eth_handle, ETH_CMD_G_MAC_ADDR, g_mac_addr);
    ESP_LOGI(TAG, "MAC Addr: %02x:%02x:%02x:%02x:%02x:%02x",
        g_mac_addr[0], g_mac_addr[1], g_mac_addr[2], g_mac_addr[3], g_mac_addr[4], g_mac_addr[5]);
#endif
}

