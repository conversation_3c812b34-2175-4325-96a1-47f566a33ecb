# ESP32 HTTP服务器移植说明

## 概述

本文档描述了如何将基于Mongoose库的HTTP服务器移植到ESP32 IDF平台。新的实现使用ESP32 IDF自带的HTTP服务器组件，提供了与原始实现相同的API接口和功能。

## 文件说明

### 新增文件
- `esp_httpServer.c` - ESP32 HTTP服务器主实现文件
- `esp_httpServer.h` - ESP32 HTTP服务器头文件
- `CMakeLists_esp_httpServer_example.txt` - CMakeLists.txt配置示例
- `README_ESP32_HTTP_SERVER.md` - 本说明文档

### 原始文件（参考，不修改）
- `components/web/mg_httpServer.c` - 原始Mongoose HTTP服务器实现

## 主要差异

### 1. 依赖库
- **原始实现**: 使用Mongoose库、pthread、Linux系统调用
- **ESP32实现**: 使用ESP-IDF HTTP服务器组件、FreeRTOS、ESP32系统API

### 2. 线程模型
- **原始实现**: 使用pthread创建线程
- **ESP32实现**: 使用FreeRTOS任务

### 3. 文件系统
- **原始实现**: 使用标准Linux文件系统
- **ESP32实现**: 使用SPIFFS或FAT文件系统

### 4. 网络接口
- **原始实现**: 使用Linux网络接口
- **ESP32实现**: 使用ESP32 WiFi和以太网接口

## 功能对比

| 功能 | 原始实现 | ESP32实现 | 状态 |
|------|----------|-----------|------|
| HTTP GET/POST处理 | ✅ | ✅ | 完整实现 |
| CORS支持 | ✅ | ✅ | 完整实现 |
| 用户登录认证 | ✅ | ✅ | 完整实现 |
| 会话管理 | ✅ | ✅ | 完整实现 |
| 静态文件服务 | ✅ | ✅ | 完整实现 |
| 设备信息API | ✅ | ✅ | 完整实现 |
| 网络信息API | ✅ | ✅ | 完整实现 |
| 终端信息API | ✅ | ✅ | 完整实现 |
| 系统重启API | ✅ | ✅ | 完整实现 |
| Ping功能 | ✅ | 🚧 | 基础框架已实现 |
| 文件上传 | ✅ | 🚧 | 待实现 |
| OTA更新 | ✅ | 🚧 | 待实现 |

## 配置要求

### 1. SDKConfig配置
在`sdkconfig`中需要启用以下配置：

```ini
# HTTP服务器
CONFIG_HTTPD_MAX_REQ_HDR_LEN=1024
CONFIG_HTTPD_MAX_URI_LEN=512
CONFIG_HTTPD_ERR_RESP_NO_DELAY=y
CONFIG_HTTPD_PURGE_BUF_LEN=32
CONFIG_HTTPD_LOG_PURGE_DATA=n

# SPIFFS文件系统（如果使用）
CONFIG_SPIFFS_MAX_PARTITIONS=3
CONFIG_SPIFFS_CACHE=y
CONFIG_SPIFFS_CACHE_WR=y

# mbedTLS（用于Base64解码）
CONFIG_MBEDTLS_BASE64_C=y

# cJSON
CONFIG_JSON_CASE_INSENSITIVE=y
```

### 2. 分区表配置
如果使用SPIFFS存储网页文件，需要在`partitions.csv`中添加：

```csv
# Name,   Type, SubType, Offset,  Size, Flags
nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 1M,
spiffs,   data, spiffs,  0x110000, 1M,
```

### 3. CMakeLists.txt配置
参考`CMakeLists_esp_httpServer_example.txt`文件中的配置示例。

## API接口说明

### 启动HTTP服务器
```c
#include "esp_httpServer.h"

// 方法1：直接启动（阻塞）
esp_err_t result = start_esp_httpServer();

// 方法2：在任务中启动（推荐）
void start_esp_httpServer_task();
```

### 停止HTTP服务器
```c
esp_err_t result = stop_esp_httpServer();
```

## HTTP API端点

### 1. 登录
- **URL**: `/web/login`
- **方法**: POST
- **Content-Type**: application/json
- **请求体**:
```json
{
    "username": "admin",
    "password": "ODg4ODg4"  // Base64编码的密码
}
```
- **响应**:
```json
{
    "result": 0,
    "session": "generated_session_token"
}
```

### 2. 获取设备信息
- **URL**: `/web/deviceInfo`
- **方法**: GET
- **Headers**: `SESSION: your_session_token`
- **响应**:
```json
{
    "result": 0,
    "data": {
        "device_name": "设备名称",
        "device_version": "2.5.0626",
        "device_sn": "设备序列号",
        "hardware_version": "ESP32-V1.0",
        "cpu_cores": 2,
        "free_heap": 123456
    }
}
```

### 3. 获取网络信息
- **URL**: `/web/networkInfo`
- **方法**: GET
- **Headers**: `SESSION: your_session_token`
- **响应**:
```json
{
    "result": 0,
    "data": {
        "ip_address": "***********00",
        "netmask": "*************",
        "gateway": "***********",
        "network_mode": 1
    }
}
```

### 4. 更新终端信息
- **URL**: `/web/terminalInfo`
- **方法**: POST
- **Headers**: `SESSION: your_session_token`
- **Content-Type**: application/json
- **请求体**:
```json
{
    "dev_name": "新设备名称",
    "aux_volume": 80
}
```

### 5. 系统重启
- **URL**: `/web/reboot`
- **方法**: POST
- **Headers**: `SESSION: your_session_token`

## 集成步骤

### 1. 复制文件
将以下文件复制到您的ESP32项目中：
- `esp_httpServer.c` -> `main/esp_httpServer.c`
- `esp_httpServer.h` -> `main/esp_httpServer.h`

### 2. 更新CMakeLists.txt
参考`CMakeLists_esp_httpServer_example.txt`更新您的`main/CMakeLists.txt`文件。

### 3. 更新main.c
在您的`main.c`文件中添加HTTP服务器启动代码：

```c
#include "esp_httpServer.h"

void app_main(void)
{
    // ... 其他初始化代码 ...
    
    // 启动HTTP服务器任务
    start_esp_httpServer_task();
    
    // ... 其他代码 ...
}
```

### 4. 添加网页文件
将您的网页文件（HTML, CSS, JS等）存储到SPIFFS分区中的`/spiffs/`目录下。

### 5. 配置变量
确保以下全局变量在您的项目中已定义：
- `g_device_alias` - 设备别名
- `g_device_sn` - 设备序列号
- `g_aux_volume` - 辅助音量
- `g_network_mode` - 网络模式

## 注意事项

1. **内存使用**: HTTP服务器会占用额外的内存，确保有足够的堆内存。
2. **任务优先级**: HTTP服务器任务优先级设置为5，可根据需要调整。
3. **会话管理**: 会话令牌在设备重启后会失效。
4. **CORS**: 默认允许所有来源的跨域请求，生产环境中建议限制。
5. **安全性**: 默认用户名/密码为admin/888888，建议在生产环境中修改。

## 故障排除

### 1. 编译错误
- 检查CMakeLists.txt中的依赖组件是否正确
- 确保包含了所有必要的头文件

### 2. 运行时错误
- 检查堆内存是否足够
- 确保WiFi连接正常
- 检查分区表配置是否正确

### 3. 网页无法访问
- 确保SPIFFS分区已正确格式化并包含网页文件
- 检查文件路径是否正确
- 验证HTTP服务器是否成功启动

## 扩展功能

如需添加更多功能，可以：
1. 在`handle_web_api`函数中添加新的命令处理
2. 创建新的URI处理函数
3. 添加新的HTTP端点到URI处理器列表

## 技术支持

如果在移植过程中遇到问题，请检查：
1. ESP-IDF版本兼容性
2. 组件依赖配置
3. 系统资源使用情况 