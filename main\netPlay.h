/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-07 10:00:15 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 15:07:45
 */

#ifndef _NETPLAYER_H_
#define _NETPLAYER_H_
#include "freertos/semphr.h"
#include "ringbuf.h"

#define RINGBUF_MP3_BLOCK_SIZE  600
#define RINGBUF_MP3_BLOCKS_NUM  50

#define RINGBUF_G722_BLOCK_SIZE  256
#define RINGBUF_G722_BLOCKS_NUM  30

#define RINGBUF_OPUS_BLOCK_SIZE  340
#define RINGBUF_OPUS_BLOCKS_NUM  60

extern ringbuf_handle_t audio_data_rb;
extern bool g_stream_ready_play;
extern unsigned int g_stream_recv_cnt;


// 新增初始化函数声明
void init_netPlay();
esp_err_t init_audio_buf();
esp_err_t ringbuf_init(int block_size, int n_blocks);
esp_err_t ringbuf_deinit();
esp_err_t Clean_All_Audio();
void setStreamRecvBuf(unsigned char *buf, int len);


void audio_data_volume_process(int16_t *pcm, int SamplesPreFrame, unsigned char channel_num);


//外部播放任务
void start_mp3Player_stream_task();
void start_g722Player_stream_task();
void start_opus_stream_task();
#endif