/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:01:46 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-04 16:52:14
 */

#ifndef _NETTOOLS_H_
#define _NETTOOLS_H_

#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_wifi.h"
#include "esp_eth.h"

// ESP32 网络接口名称结构
typedef struct {
    char ifr_name[16];
    // 其他字段可根据需要添加
} esp_ifreq_t;

extern esp_ifreq_t nif_eth0, nif_4g;

//判断网络信息数据长度
#define NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay)	\
								( (strlen(TempStatic_ip_address) > 16 || strlen(TempStatic_ip_address) < 7) || \
								(strlen(TempSubnet_Mask) > 16 || strlen(TempSubnet_Mask) < 7) || \
								(strlen(TempGateWay) > 16 || strlen(TempGateWay) < 7) )		//***************	

int if_a_string_is_a_valid_ipv4_address(const char *str);
int isValidSubnetMask(const char *mask);
int isGatewayByNetmask_Error(const char *ip,const char *mask,const char *gw);
void Get_Network_Info(char *ipaddress,char *netmask,char *gateway);
void nif_init();
bool is_valid_domain(const char *domain);
#endif