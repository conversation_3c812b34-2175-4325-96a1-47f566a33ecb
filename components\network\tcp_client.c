/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-16 12:54:42 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-16 18:04:18
 */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"
#include "esp_timer.h"
#include <stdlib.h>  // 添加malloc/free支持

#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "tcp_client.h"
#include "multicast.h"
#include "netTools.h"
// TODO: 引用外部模块
// #include "kcp/mkcp.h"

#define TCP_RECV_TIMEOUT 50000

static const char *TAG = "TCP_CLIENT";

/*****************    本地变量声明    ******************/
static _st_tcp_info st_master_tcp_info;		//主服务器TCP信息
static _st_tcp_info st_slave_tcp_info;		//备用服务器TCP信息


extern void send_online_info(void);

#ifndef NETWORK_MODE_WAN
#define NETWORK_MODE_WAN 1
#endif

#ifndef NETWORK_MODE_LAN
#define NETWORK_MODE_LAN 0
#endif

#ifndef SOURCE_NULL
#define SOURCE_NULL 0x00
#endif

#ifndef SOURCE_AUX
#define SOURCE_AUX 0x01
#endif

#ifndef SOURCE_100V_INPUT
#define SOURCE_100V_INPUT 0x0A
#endif

int tcp_connect_init()
{
	/*********主服务器信息*******/
	st_master_tcp_info.tcp_connect_status = 0;			//与主机的TCP连接状态，1-连接成功，0-连接失败
	st_master_tcp_info.tcp_exit_flag = 0xff;			//退出TCP标志:0代表TCP线程正常工作，1-需要关闭，0xFF-没有启动
	st_master_tcp_info.tcp_reconnect_flag=0;			//TCP重连标志,1-需要重连，0-不需要重连
	memset(&st_master_tcp_info.tcp_client_addr,0,sizeof(st_master_tcp_info.tcp_client_addr));	//tcp客户端连接地址
	st_master_tcp_info.tcp_sockfd=-1;					//tcp客户端句柄
	memset(&st_master_tcp_info.tcp_pkg_info,0,sizeof(st_master_tcp_info.tcp_pkg_info));	//tcp包信息
	st_master_tcp_info.tcp_clinet_fr=0;    				//tcp first response
	
	return 0;
}

int tcp_get_master_connect_status()
{
	return st_master_tcp_info.tcp_connect_status;
}

int tcp_get_master_client_fr_flag()
{
	return st_master_tcp_info.tcp_clinet_fr;
}

void tcp_set_master_client_fr_flag(int fr_flag)
{
	st_master_tcp_info.tcp_clinet_fr=fr_flag;
}

void tcp_client_exit()
{
	if(st_master_tcp_info.tcp_exit_flag!=0xff)
	{
		st_master_tcp_info.tcp_exit_flag=1;
	}
}

void tcp_client_reconnect()
{
	if(st_master_tcp_info.tcp_exit_flag == 0)
	{
		// TODO: 退出KCP接收线程 - 需要外部模块实现
		// exit_kcp_recv_thread();
		//重连标志一定要放到下面，否则可能导致死锁（因为连接上会立即再次调用exit_kcp_recv_thread函数）
		st_master_tcp_info.tcp_reconnect_flag=1;
	}
}

void host_tcp_send_data(unsigned char *send_buf, int send_len)
{
	if(send_len < PACKAGE_MIN_SIZE)
		return;
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(st_master_tcp_info.tcp_connect_status || st_slave_tcp_info.tcp_connect_status)
		{
			// 获取两个字节的命令字
			int cmd_word=(send_buf[0]<<8) + send_buf[1];
			//ESP_LOGI(TAG, "Send TCP CMD_WORD = 0x%04x", cmd_word);
			if(st_master_tcp_info.tcp_connect_status)
			{
				send(st_master_tcp_info.tcp_sockfd, send_buf, send_len, 0);
			}
			if(st_slave_tcp_info.tcp_connect_status)
			{
				send(st_slave_tcp_info.tcp_sockfd, send_buf, send_len, 0);
			}
		}
	}
}

static void reset_tcp_client_pkg(_st_tcp_info *tcp_info)
{
	memset(&tcp_info->tcp_pkg_info,0,sizeof(tcp_info->tcp_pkg_info));
}

static void tcp_pkg_handle(_st_tcp_info *tcp_info,int recvLen,unsigned char *recvBuf)
{
	bool isMasterTcp= (tcp_info == &st_master_tcp_info)?true:false;
    int	pos = 0;
    int iLength = recvLen;
    unsigned char *pData=recvBuf;
    //printf("tcp_recv_len1=%d\n",iLength);
    while(pos < iLength && !tcp_info->tcp_exit_flag && !tcp_info->tcp_reconnect_flag)
    {
        if(tcp_info->tcp_pkg_info.pkg_len + (iLength - pos) <= 8) // 原有的数据+收到的数据都不够包头长度
        {
            memcpy(tcp_info->tcp_pkg_info.pkg_data + tcp_info->tcp_pkg_info.pkg_len, pData + pos, iLength - pos);
            tcp_info->tcp_pkg_info.pkg_len += (iLength - pos);
            pos += iLength - pos;
        }
        else
        {
            if(tcp_info->tcp_pkg_info.pkg_len < 8) // 原有的数据不够包头长，所以要构成包头
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data + tcp_info->tcp_pkg_info.pkg_len, pData + pos, 8-tcp_info->tcp_pkg_info.pkg_len);
                pos += 8-tcp_info->tcp_pkg_info.pkg_len;
                tcp_info->tcp_pkg_info.pkg_len = 8; // 包头
            }
            unsigned int dataLen = (*(tcp_info->tcp_pkg_info.pkg_data + 6))*256 + (*(tcp_info->tcp_pkg_info.pkg_data + 7)); // 数据长度（+6位置是固定的）
            unsigned int idleLen	= dataLen - (tcp_info->tcp_pkg_info.pkg_len - 8) + 1;	// 一个包剩余的长度,+1是检验位

            if(iLength - pos < idleLen) // 收到的数据部分凑不够完整的包
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data+tcp_info->tcp_pkg_info.pkg_len, pData + pos, iLength - pos);
                tcp_info->tcp_pkg_info.pkg_len += iLength - pos;
                pos += iLength - pos;
            }
            else	// 可以构成完整的包
            {
                memcpy(tcp_info->tcp_pkg_info.pkg_data+tcp_info->tcp_pkg_info.pkg_len, pData + pos, idleLen);
                tcp_info->tcp_pkg_info.pkg_len += idleLen;
                pos += idleLen;

                unsigned int command = (tcp_info->tcp_pkg_info.pkg_data[0]<<8)+tcp_info->tcp_pkg_info.pkg_data[1];
                //printf("tcp:recv command=0x%04x\n",command);
                
				NetPkg_Add(NET_TYPE_TCP_SERVER,tcp_info->tcp_pkg_info.pkg_data,tcp_info->tcp_pkg_info.pkg_len);
                tcp_info->tcp_pkg_info.pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
            }
        }
    }
}

/*
Linux下常见的socket错误码：
EACCES, EPERM：用户试图在套接字广播标志没有设置的情况下连接广播地址或由于防火墙策略导致连接失败。
EADDRINUSE 98：Address already in use（本地地址处于使用状态）
EAFNOSUPPORT 97：Address family not supported by protocol（参数serv_add中的地址非合法地址）
EAGAIN：没有足够空闲的本地端口。
EALREADY 114：Operation already in progress（套接字为非阻塞套接字，并且原来的连接请求还未完成）
EBADF 77：File descriptor in bad state（非法的文件描述符）
ECONNREFUSED 111：Connection refused（远程地址并没有处于监听状态）
EFAULT：指向套接字结构体的地址非法。
EINPROGRESS 115：Operation now in progress（套接字为非阻塞套接字，且连接请求没有立即完成）
EINTR：系统调用的执行由于捕获中断而中止。
EISCONN 106：Transport endpoint is already connected（已经连接到该套接字）
ENETUNREACH 101：Network is unreachable（网络不可到达）
ENOTSOCK 88：Socket operation on non-socket（文件描述符不与套接字相关）
ETIMEDOUT 110：Connection timed out（连接超时）
*/

static void tcp_client_communication(void * arg)
{
	_st_tcp_info *tcp_info = (_st_tcp_info *)arg;
	bool isMasterTcp= (tcp_info == &st_master_tcp_info)?true:false;
	ESP_LOGI(TAG, "tcp_client_communication:isMasterTcp=%d", isMasterTcp);
	
	int i;
	int ret = -1;
	fd_set readfd;
	int pkg_count = 0;
	struct timeval timeout;
	unsigned long count = 0;

	tcp_info->tcp_exit_flag=0;
	tcp_info->tcp_reconnect_flag=0;
	tcp_info->tcp_connect_status=0;
		/*套接字信息赋值*/
	memset(&tcp_info->tcp_client_addr,0,sizeof(struct sockaddr_in));
	tcp_info->tcp_client_addr.sin_family = AF_INET; // IPV4

	//默认第一个tcp地址
	g_current_connect_tcp_id=1;
	bool isSlaveTcpAddrValid = false;
	if((if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2)) && g_host_tcp_port2>0)
	{
		isSlaveTcpAddrValid = true;
	}
	while( g_network_mode == NETWORK_MODE_WAN && !tcp_info->tcp_exit_flag )
	{
		char *tcp_addr_domian=(g_current_connect_tcp_id == 1)?g_host_tcp_addr_domain:g_host_tcp_addr_domain2;
		int tcp_port=(g_current_connect_tcp_id == 1)?g_host_tcp_port:g_host_tcp_port2;

		struct hostent *host = gethostbyname(tcp_addr_domian);
		if(host == NULL) {
			if(isSlaveTcpAddrValid)
			{
				if(g_current_connect_tcp_id == 1)
				{
					g_current_connect_tcp_id=2;
					ESP_LOGI(TAG, "DNS resolve failed for %s,try to connect TCP server2!", tcp_addr_domian);
				}
				else if(g_current_connect_tcp_id == 2)
				{
					g_current_connect_tcp_id=1;
					ESP_LOGI(TAG, "DNS resolve failed for %s,try to connect TCP server1!", tcp_addr_domian);
				}
			}
			else
			{
				ESP_LOGI(TAG, "DNS resolve failed for %s", tcp_addr_domian);
			}
			vTaskDelay(pdMS_TO_TICKS(3000));
			continue;
		}
		//将解析后的地址给到tcp_info->tcp_client_addr
		memset(&tcp_info->tcp_client_addr,0,sizeof(struct sockaddr_in));
		tcp_info->tcp_client_addr.sin_family = AF_INET;
		memcpy(&tcp_info->tcp_client_addr.sin_addr, host->h_addr_list[0], host->h_length);
		tcp_info->tcp_client_addr.sin_port = htons(tcp_port);
		//打印解析后的地址
		char ipAddress[INET_ADDRSTRLEN];
		inet_ntop(AF_INET, &tcp_info->tcp_client_addr.sin_addr,
			ipAddress, INET_ADDRSTRLEN);
		ESP_LOGI(TAG, "tcp_client_communication:DNS resolve success for %s,ipAddress=%s,port=%d", tcp_addr_domian,ipAddress,tcp_port);

		//将解析后的地址赋值给g_host_tcp_prase_ipAddress
		inet_ntop(AF_INET, &tcp_info->tcp_client_addr.sin_addr, 
			g_host_tcp_prase_ipAddress, INET_ADDRSTRLEN);

		tcp_info->tcp_connect_status=0;
		tcp_info->tcp_exit_flag=0;
		reset_tcp_client_pkg(tcp_info);
		/*创建套接字，SERVER_IPV4，TCP*/
		tcp_info->tcp_sockfd = socket(AF_INET, SOCK_STREAM, 0);
		if (tcp_info->tcp_sockfd >= 0)
		{
			ESP_LOGI(TAG, "tcp_client_communication:Creat Socket Succeed!");
		}
		else
		{
			ESP_LOGE(TAG, "tcp_client_communication:socket failed,exit thread!\n");
			close(tcp_info->tcp_sockfd);
			tcp_info->tcp_exit_flag=0xff;
			vTaskDelete(NULL);
		}

		//设置为非阻塞
		unsigned long ul = 1;
        ioctl(tcp_info->tcp_sockfd, FIONBIO, &ul); //设置为非阻塞模式

		//connect会立即返回，可能返回成功，也可能返回失败。如果连接的服务器在同一台主机上，那么在调用connect 建立连接时，连接通常会立即建立成功。
		int conn = connect(tcp_info->tcp_sockfd, (struct sockaddr *)&tcp_info->tcp_client_addr, sizeof(struct sockaddr_in));
		if (conn == 0) {
			ESP_LOGI(TAG, "Socket Connect Success Immediately.");
		}
		else 
		{
			ESP_LOGI(TAG, "Get The Connect Result by select().");
			int connect_flag=0;
			int tcp_errno = errno;
			if (tcp_errno == EINPROGRESS)
			{
				fd_set rfds, wfds;
				struct timeval tv;

				FD_ZERO(&rfds);
				FD_ZERO(&wfds);
				FD_SET(tcp_info->tcp_sockfd, &rfds);
				FD_SET(tcp_info->tcp_sockfd, &wfds);

				/* set select() time out */
				tv.tv_sec = 4;
				tv.tv_usec = 0;
				int selres = select(tcp_info->tcp_sockfd + 1, &rfds, &wfds, NULL, &tv);
				switch (selres)
				{
					case -1:
						ESP_LOGE(TAG, "select error");
						ret = -1;
						break;
					case 0:
						ESP_LOGE(TAG, "select time out");
						ret = -1;
						break;
					default:
					if (FD_ISSET(tcp_info->tcp_sockfd, &rfds) || FD_ISSET(tcp_info->tcp_sockfd, &wfds))
					{
						if( connect(tcp_info->tcp_sockfd, (struct sockaddr *)&tcp_info->tcp_client_addr, sizeof(struct sockaddr_in)) !=0 )
						{
							int err = errno;
							if (err == EISCONN)
							{
								ESP_LOGI(TAG, "TCP:Connect Succeed1!");
								ret = 0;
							}
							else
							{
								ESP_LOGE(TAG, "TCP:connect failed,errno = %d", errno);
								ret = errno;
							}
						}
						else
						{
							ESP_LOGI(TAG, "TCP:Connect Succeed2:isMasterTcp=%d!", isMasterTcp);
							ret = 0;
						}
					}
					else
					{
						ESP_LOGE(TAG, "haha");
					}
					break;
				}
				if ( ret != 0)
				{
					if(ret == ECONNREFUSED)
					{
						int count=25;		//5s
						while( count-- && !tcp_info->tcp_exit_flag )
						{
							vTaskDelay(pdMS_TO_TICKS(200));
						}
					}
				}
				else
				{
					connect_flag=1;
				}
			}

			if(!connect_flag)
			{
				ESP_LOGE(TAG, "tcp_client_communication:Connect failed,retry...");
				if(tcp_info->tcp_exit_flag)
				{
					ESP_LOGE(TAG, "need exit_tcp,close tcp thread.");
				}
				else
				{
					close(tcp_info->tcp_sockfd);
				}

				#if !IS_DEVICE_AUDIO_MIXER
				//切换TCP主备连接
				if(g_current_connect_tcp_id == 1)
				{
					//备用服务器的IP是合法的IPV4地址，且端口号有效，那么切换到备用服务器
					if(isSlaveTcpAddrValid)
					{
						g_current_connect_tcp_id=2;
						ESP_LOGI(TAG, "Try tcp_id=2!");
					}
				}
				else if(g_current_connect_tcp_id == 2)
				{
					g_current_connect_tcp_id=1;
					ESP_LOGI(TAG, "Try tcp_id=1!");
				}
				#endif

				vTaskDelay(pdMS_TO_TICKS(1000));
				continue;
			}
		}

		if( tcp_info->tcp_exit_flag || g_network_mode == NETWORK_MODE_LAN) //模式改变或者需要退出
		{
			ESP_LOGE(TAG, "tcp_info->tcp_exit_flag || g_network_mode == NETWORK_MODE_LAN,exit");
			continue;
		}

		if(strncmp(g_host_tcp_prase_ipAddress,"192",3) && strncmp(g_host_tcp_prase_ipAddress,"169",3) && strncmp(g_host_tcp_prase_ipAddress,"127",3) && strncmp(g_host_tcp_prase_ipAddress,"10.",3) && strncmp(g_host_tcp_prase_ipAddress,"29.",3) )	//既不是192开头也不是169、127、10、29开头，代表是公网IP
		{
			g_Is_tcp_real_internet=1;
		}
		else
		{
			g_Is_tcp_real_internet=0;
		}

		//已经连接上，重新设置为阻塞模式
		ul = 0;
        ioctl(tcp_info->tcp_sockfd, FIONBIO, &ul);

		tcp_info->tcp_reconnect_flag=0;
		while(g_network_mode == NETWORK_MODE_WAN && !tcp_info->tcp_exit_flag)
		{
			if(tcp_info->tcp_reconnect_flag)
			{
				g_current_connect_tcp_id=1;	//默认第一个tcp地址
				tcp_info->tcp_connect_status=0;
				close(tcp_info->tcp_sockfd);
				break;
			}

			//开始读数据
			timeout.tv_sec = 0; //获取数据超时时间设置
			timeout.tv_usec = TCP_RECV_TIMEOUT;

			FD_ZERO(&readfd); //清空读文件描述集合
			FD_SET(tcp_info->tcp_sockfd, &readfd); //注册套接字文件描述符

			//printf("tcp select1...\n");
			ret = select(tcp_info->tcp_sockfd+1, &readfd, NULL, NULL, &timeout);
			int connect_error=0;
			switch(ret)
			{
				case -1 : //调用出错
					ESP_LOGE(TAG, "tcp:select error,reconnect");
					tcp_info->tcp_reconnect_flag=1;
					tcp_info->tcp_connect_status=0;
					connect_error=1;
					close(tcp_info->tcp_sockfd);
					break;

				case 0 : //超时
					//perror("TCP select TimeOut");
					//reset_tcp_client_pkg();
					break;

				default : //有数据可读 - 需要考虑粘包情况
					if (FD_ISSET(tcp_info->tcp_sockfd, &readfd))
					{
						/*读取数据*/
						unsigned char *recvBuf = malloc(MAX_BUF_SIZE);
						int recv_len = recv(tcp_info->tcp_sockfd, recvBuf, MAX_BUF_SIZE, 0);
						if( recv_len == 0 || recv_len == -1 )
						{
							//断开连接，重连
							ESP_LOGE(TAG, "tcp_recv_error,reconnect");
							connect_error=1;
							tcp_info->tcp_reconnect_flag=1;
							tcp_info->tcp_connect_status=0;

							//如果设备正在播放歌曲的时候TCP断开了，应该立即停止播放
							#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
							if(isMasterTcp)
							{
								if( g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX && g_media_source != SOURCE_100V_INPUT )
								{
									ESP_LOGE(TAG, "tcp disconnect,terminal play!\r\n");
									Set_zone_idle_status(NULL,  __func__, __LINE__,true);
								}
							}
							#endif

							int count=30;		//3s
							while( count-- && !tcp_info->tcp_exit_flag )
							{
								vTaskDelay(pdMS_TO_TICKS(100));
							}
							
							free(recvBuf);
							break;
						}
						else
						{
							tcp_pkg_handle(tcp_info,recv_len,recvBuf);
						}
						free(recvBuf);
					}
					break;
			}
			if(connect_error == 0 && tcp_info->tcp_connect_status == 0)
			{
				tcp_info->tcp_connect_status=1;
				tcp_info->tcp_clinet_fr=0;
				#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
				if(isMasterTcp)
				{
					//mkcp_init();
				}
				#endif
				vTaskDelay(pdMS_TO_TICKS(100));
				send_online_info();
			}
		}
	}
	close(tcp_info->tcp_sockfd);
	tcp_info->tcp_connect_status=0;
	tcp_info->tcp_exit_flag=0xff;

	// TODO: 退出KCP接收线程 - 需要外部模块实现
	// exit_kcp_recv_thread();

	ESP_LOGI(TAG, "TCP thread closed...");
	vTaskDelete(NULL);
}

void TCP_Client_Start()
{
	BaseType_t ret;
	TaskHandle_t tcp_master_pthread;

	// Create Thread for master connection
	ret = xTaskCreate(tcp_client_communication, "tcp_master_pthread", 4096, (void *)&st_master_tcp_info, 5, &tcp_master_pthread);
	if (ret != pdPASS)
	{
		ESP_LOGE(TAG, "create tcp_master_pthread failed");
	}
	else
	{
		 ESP_LOGI(TAG, "create tcp_master_pthread success!");
	}
}