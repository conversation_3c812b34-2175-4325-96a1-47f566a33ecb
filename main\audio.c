#include "esp_log.h"
#include "stdlib.h"
#include "string.h"
#include "stdbool.h"
// 头文件保留mp3解码相关
#include "mp3_decoder.h"
#include "netHandle.h"
#include "audio.h"

static const char *TAG = "audio";

// 修改全局变量区域
static i2s_chan_handle_t i2s_tx_handle = NULL;


unsigned int g_playTaskId=0;
unsigned int g_playTaskSrc=0;
unsigned int g_playIndex = 0;
unsigned int g_need_download_pos = 0;
unsigned int g_has_download_pos = 0;

unsigned int g_song_total_frames=0;
unsigned int g_audio_channel_num = 2;   //默认双声道，即使是单声道，也要转换成双声道再送给I2S
unsigned int g_audio_sample_rate = 44100;
unsigned int g_song_length = 0; //歌曲的大小（字节）

bool can_get_song_stream_flag = false;



void cleanPlayTask(bool isStopTask)
{
  if(g_playTaskSrc !=TASK_SRC_NONE)
  {
    g_stream_ready_play = false;

    //加入标志，下一曲时isStopTask=false,不重置g_playTaskId和task_details
    if(isStopTask)
    {
        g_playTaskId = 0;
        g_playTaskSrc = TASK_SRC_NONE;
    }

    g_need_download_pos = 0;
    g_has_download_pos = 0;

    vTaskDelay(100/portTICK_PERIOD_MS);
  }
}