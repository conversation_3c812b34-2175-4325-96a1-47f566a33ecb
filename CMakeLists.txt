# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.16)

# 设置编译选项，必须在project()之前
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-error=pointer-sign -Wno-error=char-subscripts -Wno-error=incompatible-pointer-types -Wno-error=sizeof-pointer-memaccess")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-error=pointer-sign -Wno-error=char-subscripts -Wno-error=incompatible-pointer-types -Wno-error=sizeof-pointer-memaccess")

include($ENV{ADF_PATH}/CMakeLists.txt)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(esp32NetSpeaker)