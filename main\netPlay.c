#include <string.h>
#include <unistd.h>
#include "sysconf.h"
#include "recv_stream.h"
#include "netPlay.h"
#include "freertos/semphr.h"

static const char *TAG = "NetPlay";
//创建互斥锁
ringbuf_handle_t audio_data_rb=NULL;
bool g_stream_ready_play = false;
unsigned int g_stream_recv_cnt=0;
static SemaphoreHandle_t mutex_cleanAudio = NULL;
static SemaphoreHandle_t ringbuf_mutex = NULL;


void init_netPlay()
{
    // 只需创建一次互斥锁
    if (!ringbuf_mutex) {
        ringbuf_mutex = xSemaphoreCreateMutex();
        if (!ringbuf_mutex) {
            ESP_LOGE(TAG, "Failed to create ringbuf mutex");
            return;
        }
    }
}

esp_err_t Clean_All_Audio()
{
    if(!mutex_cleanAudio)
	{
		mutex_cleanAudio = xSemaphoreCreateMutex();
        if (!mutex_cleanAudio) {
            ESP_LOGE(TAG, "Failed to create cleanAudio mutex");
            return ESP_FAIL;
        }
	}
	xSemaphoreTake(mutex_cleanAudio, portMAX_DELAY);	//获取互斥锁，防止多次调用Clean_All_Audio()函数导致互相等待死锁
    g_stream_recv_cnt=0;
    g_stream_ready_play=0;
    bool isPlayTaskRunning = false;

	if(g_concentrated_start)
    {
        g_concentrated_start=0;
        isPlayTaskRunning = true;
    }
    else if(g_collector_run_flag)
    {
        g_collector_run_flag=0;
        isPlayTaskRunning = true;
    }
    
    if(isPlayTaskRunning)
    {
		vTaskDelay(pdMS_TO_TICKS(200));
        if(audio_data_rb)
        {
            ringbuf_deinit();
        }
    }
	xSemaphoreGive(mutex_cleanAudio);	//释放互斥锁，允许其他任务调用Clean_All_Audio()函数
    return ESP_OK;
}

esp_err_t init_audio_buf()
{
    if(!audio_data_rb)
    {
        ringbuf_init(RINGBUF_MP3_BLOCK_SIZE, RINGBUF_MP3_BLOCKS_NUM);
    }
    return ESP_OK;
}


esp_err_t ringbuf_init(int block_size, int n_blocks)
{
    xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
    if(audio_data_rb) {
        xSemaphoreGive(ringbuf_mutex);
        return ESP_OK;
    }
    // 创建环形缓冲区
    audio_data_rb = rb_create(block_size, n_blocks);
    xSemaphoreGive(ringbuf_mutex);
    
    if (!audio_data_rb) {
        ESP_LOGE(TAG, "Failed to create ring buffer");
        return ESP_FAIL;
    }

    return ESP_OK;
}

esp_err_t ringbuf_deinit()
{
    if (!ringbuf_mutex)
    {
        return ESP_FAIL;
    }

    xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
    if (!audio_data_rb) {
        xSemaphoreGive(ringbuf_mutex);
        return ESP_FAIL;
    }
    // 仅销毁环形缓冲区，保留互斥锁
    if (audio_data_rb) {
        rb_destroy(audio_data_rb);
        audio_data_rb = NULL;
        ESP_LOGI(TAG, "ringbuf_deinit: audio_data_rb destroy success!");
    }
    xSemaphoreGive(ringbuf_mutex);
    return ESP_OK;
}


void setStreamRecvBuf(unsigned char *buf, int len)
{
    xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
    if (!audio_data_rb) {
        xSemaphoreGive(ringbuf_mutex);
        return;
    }
    rb_write(audio_data_rb,(char*)buf,len,0);
    xSemaphoreGive(ringbuf_mutex);
    g_stream_recv_cnt++;

    if(g_stream_ready_play)
    {
        return;
    }
    //根据音源判断所需填充的音频数据量
    //todo 还需要结合网络模式（局域网、广域网）判断
    if(g_media_source == SOURCE_LOCAL_PLAY || g_media_source == SOURCE_TIMING || g_media_source == SOURCE_AUDIO_MIXED
        || g_media_source == SOURCE_PHONE_GATEWAY || g_media_source == SOURCE_FIRE_ALARM)
    {
        if(g_concentrated_song_type == 1)	//MP3
        {
            int after_count=10;         //concentrated_write_pos=11时开始读取，缓存1.067*11*1000/40=266ms，TCP模式下缓存不变
            if(g_network_mode == NETWORK_MODE_WAN && g_Is_tcp_real_internet)
            {
                after_count=30;
            }
            if(g_stream_recv_cnt >= after_count)
            {
                g_stream_ready_play=true;
            }
        }
        else if(g_concentrated_song_type == 2)	//WAV
        {
            int after_count=10; 
            if(g_concentrated_song_channels==2)
            {
                after_count = 18;
            }
            else
            {
                after_count = 10;
            }
            if(g_network_mode == NETWORK_MODE_WAN && g_Is_tcp_real_internet)          //1*31*1000/176=164ms
            {
                after_count = 20;
            }

            if(g_stream_recv_cnt >= after_count)
            {
                g_stream_ready_play=true;
            }
        }
    }
    if(g_stream_ready_play)
    {
        ESP_LOGI(TAG, "g_stream_ready_play=true");
    }
}



//音量表中数据表示的是音频通路数字部分的Gain值
//4095表示0dB,为0时表示Mute。音量可调整增益表中只做负增益
//需要正增益设置每个source源的预增益
//两级音量之间的计算公式为 "20*log(Vol1/Vol2)"，单位dB
const uint16_t mSysVol[100 + 1] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	5, 10, 18, 27, 38, 50, 63, 79, 94, 112, /*-31.25dB*/
	126, 141, 135, 178, 199, 217, 237, 258, 282, 307, /*-22.5dB*/
	325, 345, 365, 387, 410, 434, 460, 487, 516, 546, /*-17.5dB*/
	562, 579, 595, 613, 631, 649, 668, 688, 708, 728, /*-15dB*/
	750, 772, 794, 817, 841, 866, 891, 917, 944, 971, /*-12.5db*/
	1000, 1029, 1059, 1090, 1122, 1154, 1188, 1223, 1259, 1295, /*-10db*/
	1333, 1372, 1412, 1453, 1496, 1539, 1584, 1631, 1678, 1727, /*-7.5dB*/
	1778, 1830, 1883, 1938, 1995, 2053, 2113, 2175, 2238, 2303, /*-5dB*/
	2371, 2440, 2511, 2584, 2660, 2738, 2817, 2900, 2984, 3072, /*-2.5dB*/
	3161, 3254, 3349, 3446, 3547, 3651, 3757, 3867, 3980, 4095/*0db*/
};

int16_t limit_value_16bit(int32_t value)
{
    int16_t result;
    if(value<-32767)
    {
        result = -32767;
    }
    else if(value>32766)
    {
        result=32766;
    }
    else
    {
        result=(int16_t)value;
    }
    return result;
}

void audio_data_volume_process(int16_t *pcm, int SamplesPreFrame, unsigned char channel_num)
{
    int i;
    int sysSource = get_system_source();
    //printf("dac0_db=%d\n",Calculate_amplitude(buffer_pcm_16,SamplesPreFrame*2));
    int t_sysvol=mSysVol[(unsigned int)(g_system_volume*g_sub_volume*0.01)];
    if(sysSource == SOURCE_NET_PAGING)
    {
        t_sysvol=mSysVol[(unsigned int)(pager_property.volume*g_sub_volume*0.01)];
    }

    //如果是网络音源（除寻呼、采集外），需要先衰减10dB，与之前的控制设备兼容
    if( sysSource != SOURCE_NET_PAGING && !(sysSource >= SOURCE_AUDIO_COLLECTOR_BASE && sysSource <= SOURCE_AUDIO_COLLECTOR_MAX)\
        &&  sysSource != SOURCE_AUDIO_MIXED && sysSource != SOURCE_PHONE_GATEWAY)
    {
        for(i=0; i<SamplesPreFrame; i++)
        {
            if(channel_num == 2)
            {
                //1295为-10dB
                pcm[2 * i + 0] = limit_value_16bit( ((((int32_t)pcm[2 * i + 0]) * 1295 + 2048) >> 12) );
                pcm[2 * i + 1] = limit_value_16bit( ((((int32_t)pcm[2 * i + 1]) * 1295 + 2048) >> 12) );
            }
            else if(channel_num == 1)
            {
                pcm[i] = limit_value_16bit( ((((int32_t)pcm[i]) * 1295 + 2048) >> 12) );
            }
        }
    }

    for(i=0; i<SamplesPreFrame; i++)
    {
        if(channel_num == 2)
        {
            pcm[2 * i + 0] = limit_value_16bit( (((((int32_t)pcm[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
            pcm[2 * i + 1] = limit_value_16bit( (((((int32_t)pcm[2 * i + 1]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
        }
        else
        {
            pcm[i] = limit_value_16bit( (((((int32_t)pcm[i]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
        }
    }
}