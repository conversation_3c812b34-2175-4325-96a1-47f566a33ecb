// ESP32 NetSpeaker HTTP Server Integration Example
// 这个文件展示了如何在您的main.c中集成新的HTTP服务器

#include <stdio.h>
#include <string.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_log.h>
#include <esp_system.h>
#include <esp_netif.h>
#include <esp_spiffs.h>
#include <nvs_flash.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>

#include "esp_httpServer.h"
#include "sysconf.h"
#include "const.h"

static const char *TAG = "MAIN";

// WiFi事件组
static EventGroupHandle_t s_wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0

// 全局变量定义（这些变量在原始项目中应该已经存在）
char g_device_alias[128] = "ESP32 NetSpeaker";
char g_device_sn[32] = "ESP32001";
int g_aux_volume = 50;
int g_network_mode = NETWORK_MODE_LAN;

// WiFi事件处理函数
static void event_handler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi连接断开，重新连接...");
        esp_wifi_connect();
        xEventGroupClearBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "获取到IP地址:" IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

// 初始化SPIFFS文件系统
static esp_err_t init_spiffs(void)
{
    ESP_LOGI(TAG, "初始化SPIFFS");
    
    esp_vfs_spiffs_conf_t conf = {
        .base_path = "/spiffs",
        .partition_label = NULL,
        .max_files = 5,
        .format_if_mount_failed = true
    };
    
    esp_err_t ret = esp_vfs_spiffs_register(&conf);
    
    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "无法挂载或格式化文件系统");
        } else if (ret == ESP_ERR_NOT_FOUND) {
            ESP_LOGE(TAG, "无法找到SPIFFS分区");
        } else {
            ESP_LOGE(TAG, "初始化SPIFFS失败 (%s)", esp_err_to_name(ret));
        }
        return ret;
    }
    
    size_t total = 0, used = 0;
    ret = esp_spiffs_info(NULL, &total, &used);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "获取SPIFFS信息失败 (%s)", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "分区大小: total: %d, used: %d", total, used);
    }
    
    return ESP_OK;
}

// 初始化WiFi
static void wifi_init_sta(void)
{
    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    // 配置WiFi连接信息（请根据您的网络环境修改）
    wifi_config_t wifi_config = {
        .sta = {
            .ssid = "YourWiFiSSID",
            .password = "YourWiFiPassword",
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .capable = true,
                .required = false
            },
        },
    };
    
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "wifi_init_sta finished.");

    /* 等待WiFi连接成功或失败 */
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
            WIFI_CONNECTED_BIT,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY);

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "连接到WiFi成功");
    } else {
        ESP_LOGE(TAG, "连接到WiFi失败");
    }
}

// 系统配置保存函数（占位符实现）
void save_sysconf(char *section, char *key) {
    // 这里应该实现实际的配置保存逻辑
    ESP_LOGI(TAG, "保存配置: [%s] %s", section, key);
}

// 创建示例网页文件
static void create_sample_web_files(void) {
    // 创建简单的index.html文件
    FILE *f = fopen("/spiffs/index.html", "w");
    if (f != NULL) {
        fprintf(f, "<!DOCTYPE html>\n"
                   "<html>\n"
                   "<head>\n"
                   "    <title>ESP32 NetSpeaker</title>\n"
                   "    <meta charset=\"UTF-8\">\n"
                   "    <style>\n"
                   "        body { font-family: Arial, sans-serif; margin: 40px; }\n"
                   "        .container { max-width: 600px; margin: 0 auto; }\n"
                   "        .button { background-color: #4CAF50; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }\n"
                   "        .button:hover { background-color: #45a049; }\n"
                   "        input[type=text], input[type=password] { width: 100%%; padding: 8px; margin: 5px 0; }\n"
                   "    </style>\n"
                   "</head>\n"
                   "<body>\n"
                   "    <div class=\"container\">\n"
                   "        <h1>ESP32 NetSpeaker 控制面板</h1>\n"
                   "        \n"
                   "        <div id=\"loginForm\">\n"
                   "            <h2>登录</h2>\n"
                   "            <input type=\"text\" id=\"username\" placeholder=\"用户名\" value=\"admin\">\n"
                   "            <input type=\"password\" id=\"password\" placeholder=\"密码\" value=\"888888\">\n"
                   "            <button class=\"button\" onclick=\"login()\">登录</button>\n"
                   "        </div>\n"
                   "        \n"
                   "        <div id=\"controlPanel\" style=\"display:none;\">\n"
                   "            <h2>设备控制</h2>\n"
                   "            <button class=\"button\" onclick=\"getDeviceInfo()\">获取设备信息</button>\n"
                   "            <button class=\"button\" onclick=\"getNetworkInfo()\">获取网络信息</button>\n"
                   "            <button class=\"button\" onclick=\"reboot()\">重启设备</button>\n"
                   "            <div id=\"result\"></div>\n"
                   "        </div>\n"
                   "    </div>\n"
                   "    \n"
                   "    <script>\n"
                   "        let sessionToken = '';\n"
                   "        \n"
                   "        function login() {\n"
                   "            const username = document.getElementById('username').value;\n"
                   "            const password = btoa(document.getElementById('password').value);\n"
                   "            \n"
                   "            fetch('/web/login', {\n"
                   "                method: 'POST',\n"
                   "                headers: { 'Content-Type': 'application/json' },\n"
                   "                body: JSON.stringify({ username, password })\n"
                   "            })\n"
                   "            .then(response => response.json())\n"
                   "            .then(data => {\n"
                   "                if (data.result === 0) {\n"
                   "                    sessionToken = data.session;\n"
                   "                    document.getElementById('loginForm').style.display = 'none';\n"
                   "                    document.getElementById('controlPanel').style.display = 'block';\n"
                   "                } else {\n"
                   "                    alert('登录失败');\n"
                   "                }\n"
                   "            });\n"
                   "        }\n"
                   "        \n"
                   "        function getDeviceInfo() {\n"
                   "            fetch('/web/deviceInfo', {\n"
                   "                headers: { 'SESSION': sessionToken }\n"
                   "            })\n"
                   "            .then(response => response.json())\n"
                   "            .then(data => {\n"
                   "                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';\n"
                   "            });\n"
                   "        }\n"
                   "        \n"
                   "        function getNetworkInfo() {\n"
                   "            fetch('/web/networkInfo', {\n"
                   "                headers: { 'SESSION': sessionToken }\n"
                   "            })\n"
                   "            .then(response => response.json())\n"
                   "            .then(data => {\n"
                   "                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';\n"
                   "            });\n"
                   "        }\n"
                   "        \n"
                   "        function reboot() {\n"
                   "            if (confirm('确定要重启设备吗?')) {\n"
                   "                fetch('/web/reboot', {\n"
                   "                    method: 'POST',\n"
                   "                    headers: { 'SESSION': sessionToken }\n"
                   "                });\n"
                   "            }\n"
                   "        }\n"
                   "    </script>\n"
                   "</body>\n"
                   "</html>");
        fclose(f);
        ESP_LOGI(TAG, "示例网页文件创建完成");
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 NetSpeaker 启动中...");

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化文件系统
    ESP_ERROR_CHECK(init_spiffs());
    
    // 创建示例网页文件
    create_sample_web_files();

    // 初始化WiFi
    ESP_LOGI(TAG, "初始化WiFi");
    wifi_init_sta();

    // 等待一段时间确保网络稳定
    vTaskDelay(pdMS_TO_TICKS(2000));

    // 启动HTTP服务器
    ESP_LOGI(TAG, "启动HTTP服务器");
    start_esp_httpServer_task();

    ESP_LOGI(TAG, "系统初始化完成");
    ESP_LOGI(TAG, "请在浏览器中访问设备IP地址来使用Web控制面板");

    // 主循环
    while (1) {
        // 在这里可以添加其他系统任务
        ESP_LOGI(TAG, "系统运行中，空闲堆内存: %d bytes", esp_get_free_heap_size());
        vTaskDelay(pdMS_TO_TICKS(30000)); // 每30秒打印一次状态
    }
}

/*
使用说明：

1. 将此文件内容集成到您的main.c中
2. 根据您的网络环境修改WiFi配置
3. 确保在CMakeLists.txt中添加了所需的依赖
4. 编译并烧录到ESP32
5. 通过串口监视器查看设备获取的IP地址
6. 在浏览器中访问该IP地址
7. 使用用户名admin和密码888888登录
8. 测试各种API功能

注意事项：
- 确保有足够的堆内存
- 根据需要调整任务优先级
- 在生产环境中修改默认密码
- 根据实际需求修改网页界面
*/ 