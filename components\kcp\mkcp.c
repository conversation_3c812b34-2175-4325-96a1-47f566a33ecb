#include "sysconf.h"
#include "mkcp.h"
#include <stdlib.h>  // 添加malloc/free支持

static pthread_mutex_t mutex_kcp=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
static pthread_mutex_t mutex_exit_kcp=PTHREAD_MUTEX_INITIALIZER;	//集中模式歌曲超时检测任务锁
static int kcp_socket=-1;//kcp socket
static ikcpcb *ikcp=NULL;
static pthread_t kcp_recv_pthread;
static pthread_t kcp_update_pthread;
static int g_kcp_exit_flag=0;

_stkcpInfo stkcpComm;

unsigned int mkcp_getconv()
{
   static unsigned int kcp_conv=0;
   if(!kcp_conv)
   {
      #if IS_DEVICE_AUDIO_MIXER
      kcp_conv=((g_mixer_mac_addr[0]+g_mixer_mac_addr[1]+g_mixer_mac_addr[2])<<24)+(g_mixer_mac_addr[3]<<16)+(g_mixer_mac_addr[4]<<8)+(g_mixer_mac_addr[5]);
      #else
      kcp_conv=((g_mac_addr[0]+g_mac_addr[1]+g_mac_addr[2])<<24)+(g_mac_addr[3]<<16)+(g_mac_addr[4]<<8)+(g_mac_addr[5]);
      #endif
      printf("mkcp_getconv=%u\n",kcp_conv);
   }
   return kcp_conv;
}

void mkcp_update()
{
    //判断KCP是否有效
    //mkcp_update需要做互斥锁？
    pthread_mutex_lock(&mutex_kcp);
   
    if(!ikcp)
    {
        pthread_mutex_unlock(&mutex_kcp);
        return;
    }
    ikcp_update(ikcp,iclock());   // ikcp_update() call ikcp_flush() and then kcp_callback(), and maybe close(true)
    pthread_mutex_unlock(&mutex_kcp);
}

static void kcp_sendData(const char *buf, int len)
{
    if(kcp_socket == -1)
      return;
    struct sockaddr_in kcp_send_sockAddr;
    memset (&kcp_send_sockAddr, 0, sizeof(kcp_send_sockAddr));
    kcp_send_sockAddr.sin_family = AF_INET;
    #if !IS_DEVICE_AUDIO_MIXER
    if(g_current_connect_tcp_id == 1)
    {
      inet_pton(AF_INET, g_host_tcp_prase_ipAddress, &kcp_send_sockAddr.sin_addr);
      //打印kcp_send_sockAddr.sin_addr
      //printf("kcp_send_sockAddr.sin_addr=%s\n",inet_ntoa(kcp_send_sockAddr.sin_addr));
      kcp_send_sockAddr.sin_port = htons (g_host_kcp_port);
    }
    else if(g_current_connect_tcp_id == 2)
    {
      inet_pton(AF_INET, g_host_tcp_prase_ipAddress, &kcp_send_sockAddr.sin_addr);
      kcp_send_sockAddr.sin_port = htons (g_host_kcp_port2);
    }
    #else
    if(tcp_get_master_connect_status() == 1)
    {
        inet_pton(AF_INET, g_host_tcp_prase_ipAddress, &kcp_send_sockAddr.sin_addr);
        kcp_send_sockAddr.sin_port = htons (g_host_kcp_port);
    }
    else if(tcp_get_slave_connect_status() == 1)
    {
        inet_pton(AF_INET, g_host_tcp_prase_ipAddress, &kcp_send_sockAddr.sin_addr);
        kcp_send_sockAddr.sin_port = htons (g_host_kcp_port2);
    }
    #endif
    int sendLen=sendto(kcp_socket, buf, len, 0,(struct sockaddr*)&kcp_send_sockAddr, sizeof(kcp_send_sockAddr));
    if(sendLen == -1)
    {
      perror("kcp_sendData\n");
    }
}
    
void mkcp_sendBychannel(int channel,const char *buf,int len)
{
  if(channel == 0)
  {
      printf("send NonKCP pkg,len=%d\n",len);
      char *sendBuf = malloc(MAX_BUF_SIZE);
      memcpy(sendBuf, buf, len);
      memset(sendBuf,0,4);

      kcp_sendData(sendBuf,len);
      free(sendBuf);
  }
  else
  {
    mkcp_sendData(buf,len);
  }

}

void mkcp_sendData(const char *buf,int len)
{
    pthread_mutex_lock(&mutex_kcp);
    if(!ikcp)
    {
       pthread_mutex_unlock(&mutex_kcp);
       return;
    }
    ikcp_send(ikcp,buf,len);
    ikcp_flush(ikcp);
    pthread_mutex_unlock(&mutex_kcp);
}



void kcp_send_heartbeat()
{
	#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
	unsigned char send_buf[8];
	unsigned int conv=mkcp_getconv();
	memcpy(send_buf+4,&conv,4);
	mkcp_sendBychannel(0,send_buf,8);	//send zero-packages with kcp info(24 bytes)
	#endif
}


// KCP的下层协议输出函数，KCP需要发送数据时会调用它
// buf/len 表示缓存和长度
// user指针为 kcp对象创建时传入的值，用于区别多个 KCP对象
int udp_output(const char *buf, int len, ikcpcb *kcp, void *user)
{
    kcp_sendData(buf,len);
    return 0;
}


void exit_kcp_recv_thread()
{
    pthread_mutex_lock(&mutex_exit_kcp);
    if(kcp_socket!=-1)
    {
      g_kcp_exit_flag=1;
      pthread_join(kcp_update_pthread,NULL);
      pthread_join(kcp_recv_pthread,NULL);
      g_kcp_exit_flag=0;
    }
    pthread_mutex_unlock(&mutex_exit_kcp);
}

/*********************************************************************
 * @fn      kcp_recv_client
 *
 * @brief   创建一个线程来接收KCP流
 *
 * @param   void
 *
 * @return  none
 */
void *kcp_recv_client(void)
{
    int ret, i;
    int Rxlen=0;
    int Index = 0;
    int Pkg_Length = 0;
    int Payload_Length = 0;
    fd_set readfd;
    unsigned char *sendBuf = malloc(MAX_BUF_SIZE);
    struct sockaddr_in Host_Addr;
    socklen_t g_sockaddr_len = sizeof(struct sockaddr_in);
    struct timeval timeout;

    /*填充地址信息*/
    memset(&Host_Addr, 0, sizeof(Host_Addr));         // 清空地址信息
    Host_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
    if(g_current_connect_tcp_id == 1)
      Host_Addr.sin_port = htons(g_host_kcp_port);      // 注册端口
    else if(g_current_connect_tcp_id == 2)
      Host_Addr.sin_port = htons(g_host_kcp_port2);      // 注册端口

    /*创建套接字*/
    if ((kcp_socket = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
      perror("kcp_recv_client create error");
      exit(0);
    }
    else
    {
      //printf("kcp_recv_client create success!\n");
    }

    /*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
    int opt = 1;
    if((setsockopt(kcp_socket,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
    {
      perror("setsockopt SO_REUSEADDR failed");
      close(kcp_socket);
      exit(0);
    }
    #ifndef USE_PC_SIMULATOR
    /*绑定地址到套接字*/
    ret = bind(kcp_socket, (struct sockaddr *)&Host_Addr, sizeof(Host_Addr));
    if (ret < 0)
    {
      perror("kcp_recv_client call bind error");
      close(kcp_socket);
      exit(0);
    }
    else
    {
      printf("kcp_recv_client bind socket success!\n");
    }
    #endif
    

    while(!g_kcp_exit_flag)
    {	
      timeout.tv_sec = 0;      // 获取数据超时时间设置
		  timeout.tv_usec = 200000;
      FD_ZERO(&readfd);                // 清空读文件描述集合
      FD_SET(kcp_socket, &readfd); // 注册套接字文件描述符
      int fdmax=kcp_socket;

      ret = select(fdmax+1, &readfd, NULL, NULL, &timeout);
      switch(ret)
      {
        case -1 : // 调用出错
          perror("multicast pthread call select error");
          break;
        
        case 0 : // 超时
          //printf("multicast pthread select timeout!!!\n");
          break;
          
        default : // 有数据可读
          if (FD_ISSET(kcp_socket, &readfd))
          {
              Rxlen = recvfrom(kcp_socket, &RxBuf[Index], MAX_BUF_SIZE, 0, (struct sockaddr *)&Host_Addr, &g_sockaddr_len);

              IUINT32 conv;
              memcpy(&conv, RxBuf, 4);
              if( conv == 0 )     //判断是不是心跳包
              {
                  //不处理
                  printf("KCP heartbeat,len=%d...\n",Rxlen);
                  break;
              }

              pthread_mutex_lock(&mutex_kcp);
              if( ikcp_input(ikcp, RxBuf, Rxlen) !=0 )   //input error (conv is not match)
              {
                  pthread_mutex_unlock(&mutex_kcp);
                  break;
              }
              ikcp_update(ikcp,iclock());

              while(1)
              {
                  memset(RxBuf,0,MAX_BUF_SIZE);
                  int len=0;
                  if( (len=ikcp_recv(ikcp, RxBuf, MAX_BUF_SIZE)) <=0 )
                  {
                      break;
                  }
                  //处理
                  //printf("stNetworkRecv.write_cunt=%d,len=%d\n",stNetworkRecv.write_cunt,len);
                  kcp_concentrated_ser_data(RxBuf,len);
              }
              pthread_mutex_unlock(&mutex_kcp);
          }
          break;
      }
    }

    close(kcp_socket);
    kcp_socket=-1;
    printf("kcp_recv_client exit...\n");
    free(sendBuf);
    pthread_exit(NULL);
}

/*********************************************************************
 * @fn      start_kcp_recv_client
 *
 * @brief   启动组播命令接收客户端
 *
 * @param
 *
 *
 * @return
 */
void start_kcp_recv_client(void)
{
	int ret = -1;
	pthread_attr_t pthread_attr;
	//pthread_attr_init(&pthread_attr);
	//pthread_attr_setdetachstate(&pthread_attr, PTHREAD_CREATE_DETACHED);
	ret = pthread_create(&kcp_recv_pthread, NULL, (void *)kcp_recv_client, NULL);
	if (ret < 0)
	{
		perror("start_kcp_recv_client create");
	}
	else
	{
		printf("start_kcp_recv_client succeed!\n");
	}
	//pthread_attr_destroy(&pthread_attr);
}




void* Kcp_Update_Thread()
{
  printf("Kcp_Update_Thread enter...\n");
	while(!g_kcp_exit_flag)
	{
		if(g_network_mode == NETWORK_MODE_WAN )
		{
			mkcp_update();
		}
		usleep(5000);
	}
  printf("Kcp_Update_Thread exit...\n");
}


void Start_Kcp_Update_Thread()
{
  int ret = -1;
	pthread_attr_t Pthread_Attr;
	//pthread_attr_init(&Pthread_Attr);
  ret = pthread_create(&kcp_update_pthread, NULL, (void *)Kcp_Update_Thread, NULL);
	if (ret < 0)
	{
		perror("Start_Kcp_Update_Thread create");
	}
	else
	{
		printf("Start_Kcp_Update_Thread succeed!\n");
	}
	//pthread_attr_destroy(&Pthread_Attr);
}


void mkcp_init()
{
    pthread_mutex_lock(&mutex_kcp);
  
    if(ikcp!=NULL)
    {
      printf("ikcp!=NULL,close and then create new one!\n");
      ikcp_release(ikcp);
      ikcp=NULL;
    }
    ikcp=ikcp_create(mkcp_getconv(),(void*)0);   //conv设置为0，使得所有数据包可用
    /*
    nodelay ：是否启用 nodelay模式，0不启用；1启用。
    interval ：协议内部工作的 interval，单位毫秒，比如 10ms或者 20ms
    resend ：快速重传模式，默认0关闭，可以设置2（2次ACK跨越将会直接重传）
    nc ：是否关闭流控，默认是0代表不关闭，1代表关闭。
    */
    ikcp_wndsize(ikcp, 128, 128);    //SEND_BUF 128,RECV 128
    ikcp_nodelay(ikcp, 1, 10, 2, 1);    //FAST MODE

    ikcp->interval = 5;
    
    // 设置回调函数
    ikcp->output = udp_output;

    //创建接收线程
    exit_kcp_recv_thread();
    Start_Kcp_Update_Thread();
    start_kcp_recv_client();
  
    pthread_mutex_unlock(&mutex_kcp);
}