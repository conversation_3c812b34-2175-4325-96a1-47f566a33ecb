#include "nvs.h"
#include "esp_log.h"
#include "sysconf.h"

static const char *TAG = "sysconf";

#if 1   //无用
//设备号
char g_device_code[64];
//系统时间戳
char g_sys_timeStamp[16];

char g_mqtt_server[64];
int g_mqtt_port;
char g_tcp_server[64];
int g_tcp_port;

//MQTT订阅主题(发布主题固定)
char mqtt_sub_topic_name[96];

char g_soundCard_server_Addr[64];    //声卡服务器地址
short int g_soundCard_server_port=8688;         //声卡服务器端口
#endif


#if (DEFAULT_MODULE_ID == POWER_P32_V01)
const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x00,0x0,0x00,0x00,0x01,0x01,0x00}, \
																			 {0,0,0,0,6400,6400,0} }; 
#endif

st_dsp_firmware_feature_info dsp_firmware_feature;

unsigned int sysRunTime=0;	//系统运行时间（秒）
unsigned char g_sysBootType=3;	//系统启动类型（1：开门狗重启 2：系统复位 3：断电重启) ,默认为3
unsigned char power_on_flag = 0;//仅上电首次通知主机标志

/***************网络相关定义 START **************/
char g_ipAddress[16];					//IP地址
char g_subnet_mask[16];				//子网掩码
char g_gateway[16];					//网关
unsigned char g_mac_addr[6];   //MAC ADDR
unsigned char network_init_flag=0;	   //网络初始化完成标志

unsigned char g_network_mode=NETWORK_MODE_LAN;	//网络模式
char g_host_tcp_addr_domain[64]="************";	//主机地址(域名或者IP)
char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP
unsigned short g_host_tcp_port=49888;				    //主机TCP端口
unsigned short g_host_kcp_port;        		        //KCP端口

int g_current_connect_tcp_id=1;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
unsigned short g_host_tcp_port2;				//主机TCP端口2
unsigned short g_host_kcp_port2;        		//KCP端口2

unsigned char eth_link_status=0;				//有线网卡连接状态

/*****IP属性 ************/
unsigned char  g_IP_Assign = IP_ASSIGN_STATIC;	//IP分配方式(static or DHCP)
char g_Static_ip_address[32]="**************";		//静态IP地址
char g_Subnet_Mask[32]="*************";				//子网掩码
char g_GateWay[32]="************";					//网关
char g_Primary_DNS[32]="************";				//主DNS服务器
char g_Alternative_DNS[32]="************";			//备用DNS服务器

/***************网络相关定义 END **************/

/**************工作模式相关定义 START************/
unsigned char g_system_work_mode = WORK_MODE_CONCENTRATED;
/**************工作模式相关定义 END************/

/**********设备信息START*****************/
char g_device_alias[128];	//设备别名
/*********设备信息END******************/

/**********基础信息START****************/
unsigned char g_system_volume = DEFAULT_SYSTEM_VOLUME;	//系统音量
unsigned char g_pre_system_volume = DEFAULT_SYSTEM_VOLUME;		//系统前一音量
unsigned char g_sub_volume = DEFAULT_SYSTEM_VOLUME;		//子音量
unsigned char g_volumeCD_aux_volume=DEFAULT_SYSTEM_VOLUME;			//音控器本地音量(存在时才有效)
unsigned char g_volumeCD_net_volume=DEFAULT_SYSTEM_VOLUME;			//音控器网络音量(存在时才有效)
//20241116 支持本地音量设置
unsigned char g_aux_volume=DEFAULT_SYSTEM_VOLUME;			//本地音量

//20250305新增线路音量和麦克风音量
unsigned char g_lineVolume = DEFAULT_SYSTEM_VOLUME;	//线路音量
unsigned char g_micVolume = DEFAULT_SYSTEM_VOLUME;	//麦克风音量

unsigned char g_timing_volume = -1;            //定时音量（记录正在定时播放时调节的音量）

signed char host_ready_offline_flag=1;			   // 主机即将离线标志
signed char g_host_device_TimeOut = -1;            //主机离线计数,-1代表已经离线

unsigned int g_paging_status = PAGING_STOP;		   // 寻呼状态
unsigned int g_media_source = SOURCE_NULL;		   // 音源状态
unsigned char g_media_name[128] = {0};//当前节目名称
unsigned char g_media_status = SONG_STOP;	//媒体播放状态

unsigned char g_terminal_control_mode = Terminal_AutoControl;	//程控 手控0x10


/*******集中模式变量************/
unsigned char g_concentrated_song_type = 1;	//集中模式-歌曲类型	1为MP3 2为WAV
unsigned int  g_concentrated_song_sample_rate=44100;	//集中模式-歌曲采样率
unsigned int  g_concentrated_song_fmt=16;	//集中模式-歌曲采样精度
unsigned int  g_concentrated_song_channels=2;	//集中模式-歌曲声道数
unsigned char g_concentrated_multicast_address[16];	//集中模式-组播地址
unsigned int  g_concentrated_multicast_port;		//集中模式-组播端口
unsigned int g_concentrated_need_exit_flag = 0;		//集中模式 退出标志 0-不需要  1-需要退出
unsigned char g_concentrated_start=0;				//集中模式启动标志
unsigned char g_concentrated_playing = 0;		    //集中模式歌曲正在播放
int g_centralized_mode_timeout_count=0;			//集中模式传输超时计数
int g_centralized_mode_timing_repeat=0;         //集中模式传输超时重复次数，连续5次代表确实收不到组播数据，应该停止重发
int g_centralized_mode_timeout_pause=1;			//集中模式播歌超时检测暂停标志
int g_centralized_mode_is_existLocalSong;		//集中模式是否存在本地歌曲
bool g_IsCentralized_mode_multicast_new_cmd=false;		//集中模式组播播放是否采用新的命令
/*******集中模式变量************/

/*******音频采集变量************/
unsigned char g_collector_run_flag=0;                //音频采集器运行标志
unsigned int g_ac_sample_rate;				//音频采集器采样率
unsigned int g_ac_channels;					//音频采集器通道数
unsigned char g_ac_multicast_address[16];		//音频采集-组播地址
unsigned int g_ac_mcast_port;					//音频采集器的组播端口
unsigned char g_ac_source_id;					//音频采集器音源ID
unsigned char g_ac_source_priority;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）
/*******音频采集变量************/


unsigned char concentrate_repeat_paly_enable = 1; //是否允许播放集中模式下重新请求播放音乐

unsigned char paging_repeat_again_enable = 1;     //是否允许重新寻呼

unsigned char mixed_source_repeat_again_enable = 1;     //是否允许重新进入混音音源

unsigned char phone_gateway_source_repeat_again_enable = 1;     //是否允许重新进入电话网关音源

unsigned char g_signal_100v=0;   			//100V信号
unsigned char g_signal_aux=0;   //Aux信号

unsigned char g_system_language = CHINESE;		//系统语言

char g_device_serialNum[20]={0};    	//设备序列号

unsigned short  g_device_moduleId=DEFAULT_MODULE_ID;	//设备型号ID
int g_Is_tcp_real_internet;					//TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP）


/************系统时间************/
unsigned char sys_date_buf[20] = {"2025-05-01"};	//系统日期
unsigned char sys_time_buf[20] = {"00:00:00"};		//系统时间
unsigned char HasGotSysTime=0;		     //是否已经获取到系统时间
_stMyTime st_CurrentTime;
/****************************/
unsigned char g_allow_localSource=1;				//是否允许本地音源，收到在线音源指令时将其置于0，定时计数恢复，避免收到播放指令，还有短暂本地音乐

/******************************************************************* */

void save_sysconf(char *section,char *key)
{
    nvs_handle_t nvs_handle;
    esp_err_t err=-1;
    
    if(section==NULL) {
        return;
    }

	if( strcmp(section,INI_SETCION_NETWORK) == 0 )
	{
        err =nvs_open(INI_SETCION_NETWORK, NVS_READWRITE, &nvs_handle);
        if( err != ESP_OK )
        {
            goto __exit;
        }
        nvs_set_u8(nvs_handle, "Connect_mode", g_network_mode);
        nvs_set_str(nvs_handle, "TcpHost_addr", g_host_tcp_addr_domain);
        nvs_set_u16(nvs_handle, "TcpHost_port", g_host_tcp_port);
        nvs_set_str(nvs_handle, "TcpHost_addr2", g_host_tcp_addr_domain2);
        nvs_set_u16(nvs_handle, "TcpHost_port2", g_host_tcp_port2);
        nvs_set_u8(nvs_handle, "Ip_mode", g_IP_Assign);
        nvs_set_str(nvs_handle, "Static_IP", g_Static_ip_address);
        nvs_set_str(nvs_handle, "Subnet_mask", g_Subnet_Mask);
        nvs_set_str(nvs_handle, "GateWay", g_GateWay);
        nvs_set_str(nvs_handle, "Primary_DNS", g_Primary_DNS);
        nvs_set_str(nvs_handle, "Alternative_DNS", g_Alternative_DNS);
	}
	else if( strcmp(section,INI_SECTION_DEVICE) == 0 )
	{
        err =nvs_open(INI_SECTION_DEVICE, NVS_READWRITE, &nvs_handle);
        if( err != ESP_OK )
        {
            goto __exit;
        }
        nvs_set_str(nvs_handle, "Device_Name", g_device_alias);
        nvs_set_u8(nvs_handle, "System_Language", g_system_language);
	}
	else if( strcmp(section,INI_SECTION_BASIC) == 0 )
	{
        err =nvs_open(INI_SECTION_BASIC, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;	
        }
        nvs_set_u8(nvs_handle, "System_Volume", g_system_volume);
        nvs_set_u8(nvs_handle, "Sub_Volume", g_sub_volume);
        nvs_set_u8(nvs_handle, "VolumeControl_Aux", g_volumeCD_aux_volume);
        nvs_set_u8(nvs_handle, "VolumeControl_Net", g_volumeCD_net_volume);
        nvs_set_u8(nvs_handle, "Aux_Volume", g_aux_volume);
        nvs_set_u8(nvs_handle, "Line_Volume", g_lineVolume);
        nvs_set_u8(nvs_handle, "Mic_Volume", g_micVolume);
	}

	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
        err =nvs_open(INI_SETCION_DSP_Firmware, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        nvs_set_u16(nvs_handle, INI_KEY_DSP_MODEL_ID, g_device_moduleId);
        nvs_set_u8(nvs_handle, INI_KEY_DSP_AMIC0_SWITCH, dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L]);
        nvs_set_u16(nvs_handle, INI_KEY_DSP_AMIC0_GAIN, dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L]);
        nvs_set_u8(nvs_handle, INI_KEY_DSP_AMIC1_SWITCH, dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R]);
        nvs_set_u16(nvs_handle, INI_KEY_DSP_AMIC1_GAIN, dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R]);
        nvs_set_u8(nvs_handle, INI_KEY_DSP_AMIC2_SWITCH, dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L]);
        nvs_set_u16(nvs_handle, INI_KEY_DSP_AMIC2_GAIN, dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L]);
        nvs_set_u8(nvs_handle, INI_KEY_DSP_DAC0L_SWITCH, dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L]);
        nvs_set_u16(nvs_handle, INI_KEY_DSP_DAC0L_GAIN, dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L]);
        nvs_set_u8(nvs_handle, INI_KEY_DSP_DAC0R_SWITCH, dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R]);
        nvs_set_u16(nvs_handle, INI_KEY_DSP_DAC0R_GAIN, dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R]);
	}
	else if( strcmp(section,INI_SETCION_MFR) == 0 )
	{
        err =nvs_open(INI_SETCION_MFR, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        nvs_set_str(nvs_handle, "SN", g_device_serialNum);
	}

    __exit:
    if( err == ESP_OK )
    {
        nvs_commit(nvs_handle);
        nvs_close(nvs_handle);
        ESP_LOGI(TAG, "save_sysconf:%s succeed",section);
    }
    else
    {
        ESP_LOGE(TAG, "save_sysconf:Failed to open NVS (%s)", esp_err_to_name(err)); 
    }
}


void read_sysconf(char *section,char *key)
{
    nvs_handle_t nvs_handle;
    esp_err_t err=-1;
    
    if(section==NULL) {
        return;
    }

	if( strcmp(section,INI_SETCION_NETWORK) == 0 )
	{
        err =nvs_open(INI_SETCION_NETWORK, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        nvs_get_u8(nvs_handle, "Connect_mode", &g_network_mode);
        size_t len=63;
        nvs_get_str(nvs_handle, "TcpHost_addr", g_host_tcp_addr_domain,&len);
        nvs_get_u16(nvs_handle, "TcpHost_port", &g_host_tcp_port);
        len=63;
        nvs_get_str(nvs_handle, "TcpHost_addr2", g_host_tcp_addr_domain2,&len);
        nvs_get_u16(nvs_handle, "TcpHost_port2", &g_host_tcp_port2);
        nvs_get_u8(nvs_handle, "Ip_mode", &g_IP_Assign);
        len=30;
        nvs_get_str(nvs_handle, "Static_IP", g_Static_ip_address,&len);
        nvs_get_str(nvs_handle, "Subnet_mask", g_Subnet_Mask,&len);
        nvs_get_str(nvs_handle, "GateWay", g_GateWay,&len);
        nvs_get_str(nvs_handle, "Primary_DNS", g_Primary_DNS,&len);
        nvs_get_str(nvs_handle, "Alternative_DNS", g_Alternative_DNS,&len);

		if(g_network_mode == NETWORK_MODE_WAN)
		{
			if(	g_host_tcp_port != DEFAULT_TCP_PORT )
			{
				g_host_kcp_port = g_host_tcp_port + 1;
			}
			else
			{
				g_host_kcp_port = DEFAULT_KCP_PORT;
			}

			if(	g_host_tcp_port2 != DEFAULT_TCP_PORT )
			{
				g_host_kcp_port2 = g_host_tcp_port2 + 1;
			}
			else
			{
				g_host_kcp_port2 = DEFAULT_KCP_PORT;
			}
		}
	}
	else if( strcmp(section,INI_SECTION_DEVICE) == 0 )
	{
        err =nvs_open(INI_SECTION_DEVICE, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        size_t len=63;
        nvs_get_str(nvs_handle, "Device_Name", g_device_alias,&len);
        nvs_get_u8(nvs_handle, "System_Language", &g_system_language);
	}
	else if( strcmp(section,INI_SECTION_BASIC) == 0 )
	{
        err =nvs_open(INI_SECTION_BASIC, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        nvs_get_u8(nvs_handle, "System_Volume", &g_system_volume);
        nvs_get_u8(nvs_handle, "Sub_Volume", &g_sub_volume);
        nvs_get_u8(nvs_handle, "VolumeControl_Aux", &g_volumeCD_aux_volume);
        nvs_get_u8(nvs_handle, "VolumeControl_Net", &g_volumeCD_net_volume);
        nvs_get_u8(nvs_handle, "Aux_Volume", &g_aux_volume);
        nvs_get_u8(nvs_handle, "Line_Volume", &g_lineVolume);
        nvs_get_u8(nvs_handle, "Mic_Volume", &g_micVolume);
        
        g_pre_system_volume=g_system_volume;
	}
	else if( strcmp(section,INI_SETCION_MFR) == 0 )
	{
        err =nvs_open(INI_SETCION_MFR, NVS_READWRITE, &nvs_handle);
        if( err!= ESP_OK )
        {
            goto __exit;
        }
        size_t len=sizeof(g_device_serialNum)-1;
        nvs_get_str(nvs_handle, "SN", g_device_serialNum,&len);
	}

	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
        err =nvs_open(INI_SETCION_DSP_Firmware, NVS_READWRITE, &nvs_handle);
        #if 0   
        //nvs_open()总会​​成功​​（返回ESP_OK），并创建一个新的命名空间
        //这是预期的行为，NVS会自动创建不存在的命名空间
        if( err!= ESP_OK )
        {
            dsp_firmware_feature = dsp_default_firmware_feature;
            save_sysconf(INI_SETCION_DSP_Firmware,NULL);
            return;
        }
        #endif
        //nvs_get如果不存在，会返回ESP_ERR_NVS_NOT_FOUND
        nvs_get_u16(nvs_handle, INI_KEY_DSP_MODEL_ID, &g_device_moduleId);
        nvs_get_u8(nvs_handle, INI_KEY_DSP_AMIC0_SWITCH, &dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L]);
        nvs_get_u16(nvs_handle, INI_KEY_DSP_AMIC0_GAIN, &dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L]);
        nvs_get_u8(nvs_handle, INI_KEY_DSP_AMIC1_SWITCH, &dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R]);
        nvs_get_u16(nvs_handle, INI_KEY_DSP_AMIC1_GAIN, &dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R]);
        nvs_get_u8(nvs_handle, INI_KEY_DSP_AMIC2_SWITCH, &dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L]);
        nvs_get_u16(nvs_handle, INI_KEY_DSP_AMIC2_GAIN, &dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L]);
        nvs_get_u8(nvs_handle, INI_KEY_DSP_DAC0L_SWITCH, &dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L]);
        nvs_get_u16(nvs_handle, INI_KEY_DSP_DAC0L_GAIN, &dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L]);
        nvs_get_u8(nvs_handle, INI_KEY_DSP_DAC0R_SWITCH, &dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R]);
        nvs_get_u16(nvs_handle, INI_KEY_DSP_DAC0R_GAIN, &dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R]);

        //如果dsp_firmware_feature的元素都为0，代表不存在，则使用默认值，并保存到NVS中，下次启动时，会使用NVS中的值。
        int i=0;
        for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
        {
            if(dsp_firmware_feature.module_switch[i] != 0)
            {
                break;
            }
        }
        if(i == DSP_AUDIO_MODULE_MAX)
        {
            dsp_firmware_feature = dsp_default_firmware_feature;
            printf("default dsp settings!\n");
            save_sysconf(INI_SETCION_DSP_Firmware,NULL);
        }
	}
    
    __exit:
    if( err == ESP_OK )
    {
        nvs_close(nvs_handle);
        //ESP_LOGI(TAG, "read_sysconf:%s succeed",section);
    }
    else
    {
        ESP_LOGE(TAG, "read_sysconf:Failed to open NVS (%s)", esp_err_to_name(err)); 
    }
}

void format_sysconf()
{
    nvs_handle_t nvs_handle;
    esp_err_t err=-1;

    err =nvs_open(INI_SETCION_NETWORK, NVS_READWRITE, &nvs_handle);
    if( err == ESP_OK )
    {
        nvs_erase_all(nvs_handle);
        nvs_commit(nvs_handle);
    }
    nvs_close(nvs_handle);

    err =nvs_open(INI_SECTION_DEVICE, NVS_READWRITE, &nvs_handle);
    if( err == ESP_OK )
    {
        nvs_erase_all(nvs_handle);
        nvs_commit(nvs_handle);
    }
    nvs_close(nvs_handle);

    err =nvs_open(INI_SECTION_BASIC, NVS_READWRITE, &nvs_handle);
    if( err == ESP_OK )
    {
        nvs_erase_all(nvs_handle);
        nvs_commit(nvs_handle);
    }
    nvs_close(nvs_handle);
}