/* Play an MP3 file from HTTP

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "nvs_flash.h"
#include "sdkconfig.h"
#include "audio_element.h"
#include "audio_pipeline.h"
#include "audio_event_iface.h"
#include "audio_common.h"
#include "http_stream.h"
#include "i2s_stream.h"
#include "mp3_decoder.h"
#include "pcm_decoder.h"

#include "esp_peripherals.h"
#include "periph_wifi.h"
#include "board.h"

#include "sysconf.h"

static const char *TAG = "MP3_PLAYER";


void mp3_play_url(char *url)
{
    audio_pipeline_handle_t pipeline;
    audio_element_handle_t http_stream_reader, i2s_stream_writer, mp3_decoder;

    esp_log_level_set("*", ESP_LOG_WARN);
    esp_log_level_set(TAG, ESP_LOG_DEBUG);

    ESP_LOGI(TAG, "[ 1 ] Start audio codec chip");
    audio_board_handle_t board_handle = audio_board_init();
    audio_hal_ctrl_codec(board_handle->audio_hal, AUDIO_HAL_CODEC_MODE_DECODE, AUDIO_HAL_CTRL_START);

    ESP_LOGI(TAG, "[2.0] Create audio pipeline for playback");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    pipeline = audio_pipeline_init(&pipeline_cfg);
    mem_assert(pipeline);

    ESP_LOGI(TAG, "[2.1] Create http stream to read data");
    http_stream_cfg_t http_cfg = HTTP_STREAM_CFG_DEFAULT();
    http_stream_reader = http_stream_init(&http_cfg);

    ESP_LOGI(TAG, "[2.2] Create i2s stream to write data to codec chip");
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT();
    i2s_cfg.type = AUDIO_STREAM_WRITER;

    i2s_stream_writer = i2s_stream_init(&i2s_cfg);

    ESP_LOGI(TAG, "[2.3] Create mp3 decoder to decode mp3 file");
    mp3_decoder_cfg_t mp3_cfg = DEFAULT_MP3_DECODER_CONFIG();
    mp3_decoder = mp3_decoder_init(&mp3_cfg);

    ESP_LOGI(TAG, "[2.4] Register all elements to audio pipeline");
    audio_pipeline_register(pipeline, http_stream_reader, "http");
    audio_pipeline_register(pipeline, mp3_decoder,        "mp3");
    audio_pipeline_register(pipeline, i2s_stream_writer,  "i2s");

    ESP_LOGI(TAG, "[2.5] Link it together http_stream-->mp3_decoder-->i2s_stream-->[codec_chip]");
    const char *link_tag[3] = {"http", "mp3", "i2s"};
    audio_pipeline_link(pipeline, &link_tag[0], 3);

    ESP_LOGI(TAG, "[2.6] Set up  uri (http as http_stream, mp3 as mp3 decoder, and default output is i2s)");
    //audio_element_set_uri(http_stream_reader, "http://************:9999/Data/Program/Other/1KHz-0dB_60s_new.mp3");
    audio_element_set_uri(http_stream_reader, url);

    ESP_LOGI(TAG, "[ 3 ] Start and wait for Wi-Fi network");
    esp_periph_config_t periph_cfg = DEFAULT_ESP_PERIPH_SET_CONFIG();

    esp_periph_set_handle_t set = esp_periph_set_init(&periph_cfg);
    #if 0
    periph_wifi_cfg_t wifi_cfg = {
        .wifi_config.sta.ssid = "411",
        .wifi_config.sta.password = "mhi87654321",
    };
    esp_periph_handle_t wifi_handle = periph_wifi_init(&wifi_cfg);
    esp_periph_start(set, wifi_handle);
    periph_wifi_wait_for_connected(wifi_handle, portMAX_DELAY);
    #endif

    // Example of using an audio event -- START
    ESP_LOGI(TAG, "[ 4 ] Set up  event listener");
    audio_event_iface_cfg_t evt_cfg = AUDIO_EVENT_IFACE_DEFAULT_CFG();
    audio_event_iface_handle_t evt = audio_event_iface_init(&evt_cfg);

    ESP_LOGI(TAG, "[4.1] Listening event from all elements of pipeline");
    audio_pipeline_set_listener(pipeline, evt);

    ESP_LOGI(TAG, "[4.2] Listening event from peripherals");
    audio_event_iface_set_listener(esp_periph_set_get_event_iface(set), evt);

    ESP_LOGI(TAG, "[ 5 ] Start audio_pipeline");
    audio_pipeline_run(pipeline);

    while (1) {
        audio_event_iface_msg_t msg;
        esp_err_t ret = audio_event_iface_listen(evt, &msg, portMAX_DELAY);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "[ * ] Event interface error : %d", ret);
            continue;
        }

        if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT
            && msg.source == (void *) mp3_decoder
            && msg.cmd == AEL_MSG_CMD_REPORT_MUSIC_INFO) {
            audio_element_info_t music_info = {0};
            audio_element_getinfo(mp3_decoder, &music_info);

            ESP_LOGI(TAG, "[ * ] Receive music info from mp3 decoder, sample_rates=%d, bits=%d, ch=%d",
                     music_info.sample_rates, music_info.bits, music_info.channels);

            i2s_stream_set_clk(i2s_stream_writer, music_info.sample_rates, music_info.bits, music_info.channels);
            continue;
        }

        /* Stop when the last pipeline element (i2s_stream_writer in this case) receives stop event */
        if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT && msg.source == (void *) i2s_stream_writer
            && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
            && (((int)msg.data == AEL_STATUS_STATE_STOPPED) || ((int)msg.data == AEL_STATUS_STATE_FINISHED))) {
            ESP_LOGW(TAG, "[ * ] Stop event received");
            break;
        }
    }
    // Example of using an audio event -- END

    ESP_LOGI(TAG, "[ 6 ] Stop audio_pipeline");
    audio_pipeline_stop(pipeline);
    audio_pipeline_wait_for_stop(pipeline);
    audio_pipeline_terminate(pipeline);

    /* Terminate the pipeline before removing the listener */
    audio_pipeline_unregister(pipeline, http_stream_reader);
    audio_pipeline_unregister(pipeline, i2s_stream_writer);
    audio_pipeline_unregister(pipeline, mp3_decoder);

    audio_pipeline_remove_listener(pipeline);

    /* Stop all peripherals before removing the listener */
    esp_periph_set_stop_all(set);
    audio_event_iface_remove_listener(esp_periph_set_get_event_iface(set), evt);

    /* Make sure audio_pipeline_remove_listener & audio_event_iface_remove_listener are called before destroying event_iface */
    audio_event_iface_destroy(evt);

    /* Release all resources */
    audio_pipeline_deinit(pipeline);
    audio_element_deinit(http_stream_reader);
    audio_element_deinit(i2s_stream_writer);
    audio_element_deinit(mp3_decoder);
    esp_periph_set_destroy(set);
}




void mp3Player_url_task(void *arg) {
    // 从参数中获取URL
    char *url = (char *)arg;
    while (1)
    {
        UBaseType_t stack_high_water = uxTaskGetStackHighWaterMark(NULL);
        ESP_LOGI(TAG, "栈剩余空间: %d bytes\n", stack_high_water);
        mp3_play_url(url);
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    free(url);
    vTaskDelete(NULL);
}

void start_mp3Player_url_task(char *url) {
    char *url_copy = strdup(url);
    xTaskCreate(mp3Player_url_task, "mp3_player_url", 4096, url_copy, 2, NULL); 
}




/********************************************************************************* */

// 正确的读取回调实现
static int audio_read_callback(audio_element_handle_t el, char *buffer, int len, TickType_t timeout, void *ctx) {
    // 从你的数据源（如网络/文件）读取数据
    int bytes_read = rb_read(audio_data_rb,buffer, len, pdMS_TO_TICKS(5000));    //0xFFFFFFFF
    // 处理数据不足的情况（补静音）
    if (bytes_read < len) {
        memset(buffer + bytes_read, 0, len - bytes_read);
        ESP_LOGW(TAG, "Underrun: %d bytes missing", len - bytes_read);
        return -1;
    }
    return len;
}


static int i2s_write_cb(audio_element_handle_t el, char *buf, int len, TickType_t wait_time, void *ctx)
{
    stream_func i2s_write_func = (stream_func)ctx;
    size_t size = len;

    #if 0
    float volumeGain = g_system_volume/100.0; // 音量调整因子，0.0到1.0之间
    // 16位PCM数据（假设解码器输出为16位有符号）
    int16_t *pcm = (int16_t *)buf;
    int samples = len / sizeof(int16_t);
    
    for(int i = 0; i < samples; i++) {
        float sample = pcm[i] * volumeGain;
        // 限制幅度在16位范围内
        pcm[i] = (int16_t)(sample > 32767 ? 32767 : (sample < -32768 ? -32768 : sample));
    }
    #else
    //立体声，一个采样点是4个字节（包含左右声道，每个声道16bit）
    int samples = len / sizeof(int16_t) / 2;
    if(g_concentrated_song_channels == 1)
    {
        samples = len / sizeof(int16_t);
    }
    /*如果此处错误，则会导致系统崩掉，待解决复位问题
    W (6697) MP3_DECODER: Output aborted, 4608
    E (6697) AUDIO_ELEMENT: [mp3] ERROR_PROCESS, AEL_IO_FAIL
    W (6697) AUDIO_ELEMENT: [mp3] audio_element_on_cmd_error,3
    I (6697) MP3_DECODER: Closed
    */
    audio_data_volume_process((int16_t *)buf, samples, g_concentrated_song_channels);
    #endif
    
    int ret = i2s_write_func(el, buf, size, wait_time, ctx);
    if (ret < 0) {
        ESP_LOGE(TAG, "i2s write failed");
    }
    return len;
}

void mp3_play_stream(void *arg)
{
    audio_element_handle_t  i2s_stream_writer, mp3_decoder;

    ESP_LOGI(TAG, "[2.0] Create audio pipeline for playback");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    audio_pipeline_handle_t pipeline = audio_pipeline_init(&pipeline_cfg);
    mem_assert(pipeline);

    ESP_LOGI(TAG, "[2.2] Create i2s stream to write data to codec chip");
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT();
    i2s_cfg.type = AUDIO_STREAM_WRITER;
#if 1
    i2s_cfg.chan_cfg.dma_frame_num = 896;   //896/44100=20ms
    i2s_cfg.chan_cfg.dma_desc_num = 6;      //8*20ms=160ms
#endif
    i2s_stream_writer = i2s_stream_init(&i2s_cfg);

    //audio_element_set_input_timeout(i2s_stream_writer, 20000 / portTICK_PERIOD_MS);

    audio_element_set_write_cb(i2s_stream_writer, i2s_write_cb, audio_element_get_write_cb(i2s_stream_writer));
    printf("g_concentrated_song_sample_rate=%d,g_concentrated_song_channels=%d\n",g_concentrated_song_sample_rate,g_concentrated_song_channels);
    i2s_stream_set_clk(i2s_stream_writer, g_concentrated_song_sample_rate, 16, g_concentrated_song_channels);

    ESP_LOGI(TAG, "[2.3] Create mp3 decoder to decode mp3 file");

    if(g_concentrated_start)
    {
        if(g_concentrated_song_type == 2)	//WAV
        {
            pcm_decoder_cfg_t  wav_cfg = DEFAULT_PCM_DECODER_CONFIG();
            mp3_decoder = pcm_decoder_init(&wav_cfg);
        }
        else	//MP3
        {
            mp3_decoder_cfg_t mp3_cfg = DEFAULT_MP3_DECODER_CONFIG();
            mp3_decoder = mp3_decoder_init(&mp3_cfg);
        }
    }
    else if(g_collector_run_flag)
    {
        if(g_network_mode == NETWORK_MODE_LAN)
        {
            pcm_decoder_cfg_t  wav_cfg = DEFAULT_PCM_DECODER_CONFIG();
            mp3_decoder = pcm_decoder_init(&wav_cfg);
        }
        else
        {
            //G722
            mp3_decoder_cfg_t mp3_cfg = DEFAULT_MP3_DECODER_CONFIG();
            mp3_decoder = mp3_decoder_init(&mp3_cfg);
        }
    }
    else
    {
        pcm_decoder_cfg_t  wav_cfg = DEFAULT_PCM_DECODER_CONFIG();
        mp3_decoder = pcm_decoder_init(&wav_cfg);
    }
    
    audio_element_set_read_cb(mp3_decoder, audio_read_callback, NULL);

    ESP_LOGI(TAG, "[2.4] Register all elements to audio pipeline");
    audio_pipeline_register(pipeline, mp3_decoder,        "mp3");
    audio_pipeline_register(pipeline, i2s_stream_writer,  "i2s");

    ESP_LOGI(TAG, "[2.5] Link it together source-->mp3_decoder-->i2s_stream");

    const char *link_tag[] = {"mp3", "i2s"};
    audio_pipeline_link(pipeline, &link_tag[0], 2);
    
    // 添加事件监听
    ESP_LOGI(TAG, "[ 4 ] Set up event listener");
    audio_event_iface_cfg_t evt_cfg = AUDIO_EVENT_IFACE_DEFAULT_CFG();
    audio_event_iface_handle_t evt = audio_event_iface_init(&evt_cfg);
    audio_pipeline_set_listener(pipeline, evt);

    bool pipeline_runing = false;
    while(g_media_source == SOURCE_LOCAL_PLAY && g_concentrated_start)
    {
        //说明pipeline已经开始运行了
        if(g_stream_ready_play)
        {
            if(!pipeline_runing)
            {
                GpioSetAmpValid(true);
                ESP_LOGI(TAG, "start pipeline!");
                audio_pipeline_run(pipeline);
                pipeline_runing=true;
            }
        }

        // 事件处理部分新增
        audio_event_iface_msg_t msg;
        if (audio_event_iface_listen(evt, &msg, 0) == ESP_OK) {
            /* 当收到停止事件时 */
            if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT 
                && (msg.source == (void *)i2s_stream_writer || msg.source == (void *)mp3_decoder)
                && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
                && ((int)msg.data == AEL_STATUS_STATE_STOPPED || (int)msg.data == AEL_STATUS_STATE_FINISHED || (int)msg.data == AEL_STATUS_ERROR_TIMEOUT
                    || (int)msg.data == AEL_STATUS_ERROR_INPUT))
                {
                    ESP_LOGW(TAG, "KK[ * ] %s stopped", (msg.source == i2s_stream_writer) ? "I2S" : "MP3_DECODER");
                    break;
                }
        }
        vTaskDelay(10 / portTICK_PERIOD_MS);
    }
    
    GpioSetAmpValid(false);

    ESP_LOGI(TAG, "Stop audio_pipeline1");
    // 销毁事件接口
    audio_event_iface_destroy(evt);
    ESP_LOGI(TAG, "Stop audio_pipeline2");
    audio_pipeline_stop(pipeline);
    ESP_LOGI(TAG, "Stop audio_pipeline3");
    #if 1   //一定要加rb_done_write，否则可能因为audio_read_callback阻塞audio_data_rb，无限等待,放到ringbuf_deinit中也行
    if(audio_data_rb)
        rb_done_write(audio_data_rb);
    #endif
    ESP_LOGI(TAG, "Stop audio_pipeline4");
    audio_pipeline_wait_for_stop(pipeline);
    ESP_LOGI(TAG, "Stop audio_pipeline5");
    audio_pipeline_terminate(pipeline);
    /* Terminate the pipeline before removing the listener */
    ESP_LOGI(TAG, "Stop audio_pipeline6");
    audio_pipeline_unregister(pipeline, i2s_stream_writer);
    ESP_LOGI(TAG, "Stop audio_pipeline7");
    audio_pipeline_unregister(pipeline, mp3_decoder);
    ESP_LOGI(TAG, "Stop audio_pipeline8");
    audio_pipeline_remove_listener(pipeline);

    /* Release all resources */
    ESP_LOGI(TAG, "Stop audio_pipeline9");
    audio_pipeline_deinit(pipeline);
    ESP_LOGI(TAG, "Stop audio_pipeline10");
    audio_element_deinit(i2s_stream_writer);
    ESP_LOGI(TAG, "Stop audio_pipeline11");
    audio_element_deinit(mp3_decoder);

    //释放ringbuf
   // ringbuf_deinit();

    ESP_LOGI(TAG, "mp3_play_stream END...");
    if(g_concentrated_start)
    {
        Set_zone_idle_status(NULL,  __func__, __LINE__,true);
    }

    vTaskDelete(NULL);
}



void start_mp3Player_stream_task() {
    
    xTaskCreate(mp3_play_stream, "mp3_player_stream", 8192, NULL, 10, NULL); 
}
