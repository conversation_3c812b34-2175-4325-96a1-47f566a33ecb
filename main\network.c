#include "lwip/err.h"
#include "lwip/sys.h"
#include "lwip/inet.h"
#include "lwip/ip4_addr.h"
#include "lwip/netdb.h"
#include "lwip/sockets.h"
#include "esp_log.h"
#include "esp_http_client.h"
#include "mqtt_client.h"
#include "jsonAdvantage.h"

#include "sysconf.h"
#include "nethandle.h"
#include "audio.h"
#include "network.h"

#if 0
int g_network_status = NETWORK_LOGIN_FAILED;
#endif
//tcp连接句柄
int tcp_socket = -1;
int soundCard_tcp_socket = -1;
//mqtt连接句柄
static esp_mqtt_client_handle_t mqtt_client_handle = NULL;

static bool g_mqtt_connected = false;

#define TCP_PROTOCOL_HEADER_LEN 20

static const char *TAG = "Network";

uint32_t last_tcp_recv_time=0;

uint32_t last_soundCard_recv_time=0;

void network_init()
{
  uint64_t uuid = get_esp32_uuid();
  sprintf(g_device_code, "%lld", uuid);
  sprintf(mqtt_sub_topic_name,"/%s/%s/ctrl","trumpet1",g_device_code);
  ESP_LOGI(TAG, "Version=%s,deviceCode:%s",PRJ_VER,g_device_code);
}

esp_err_t _http_event_handler(esp_http_client_event_t *evt)
{
    static char *output_buffer; // 用于存储来自事件处理程序的http请求响应的缓冲区
    static int output_len;      // 存储读取的字节数
    switch (evt->event_id)
    {
        case HTTP_EVENT_ERROR:
            ESP_LOGD(TAG, "HTTP_EVENT_ERROR");
            break;
        case HTTP_EVENT_ON_CONNECTED:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_CONNECTED");
            break;
        case HTTP_EVENT_HEADER_SENT:
            ESP_LOGD(TAG, "HTTP_EVENT_HEADER_SENT");
            break;
        case HTTP_EVENT_ON_HEADER:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_HEADER,key=%s,value=%s", evt->header_key, evt->header_value);
            break;
        case HTTP_EVENT_ON_DATA:
            //ESP_LOGI(TAG, "HTTP_EVENT_ON_DATA,len=%d", evt->data_len);
            /*
            *  由于此示例中使用的分块编码URL返回二进制数据，因此添加了对分块编码的检查。
            *  但是，在使用分块编码的情况下，也可以使用事件处理程序。
            */
            //if (!esp_http_client_is_chunked_response(evt->client))
            {
                // 如果配置了user_data缓冲区，则将响应复制到缓冲区中
                if (evt->user_data)
                {
                    memcpy(evt->user_data + output_len, evt->data, evt->data_len>1024?1024:evt->data_len);
                }
                else
                {
                    if (output_buffer == NULL)
                    {
                        output_buffer = (char *)malloc(esp_http_client_get_content_length(evt->client));
                        output_len = 0;
                        if (output_buffer == NULL)
                        {
                            ESP_LOGE(TAG, "无法为输出缓冲区分配内存");
                            return ESP_FAIL;
                        }
                    }
                    memcpy(output_buffer + output_len, evt->data, evt->data_len);
                }
                output_len += evt->data_len;
            }

            break;
        case HTTP_EVENT_ON_FINISH:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_FINISH");
            if (output_buffer != NULL)
            {
                // 在output_buffer中累积响应。取消注释下面的行以打印累积的响应
                // ESP_LOG_BUFFER_HEX(TAG, output_buffer, output_len);
                free(output_buffer);
                output_buffer = NULL;
            }
            output_len = 0;
            break;
        case HTTP_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "HTTP_EVENT_DISCONNECTED");
            #if 0
            int mbedtls_err = 0;
            esp_err_t err = esp_tls_get_and_clear_last_error(evt->data, &mbedtls_err, NULL);
            if (err != 0)
            {
                ESP_LOGI(TAG, "上一个esp错误代码：0x%x", err);
                ESP_LOGI(TAG, "上次mbedtls失败：0x%x", mbedtls_err);
            }
            #endif
            if (output_buffer != NULL)
            {
                free(output_buffer);
                output_buffer = NULL;
            }
            output_len = 0;
            break;
        default:
            break;    
    }
    return ESP_OK;
}

esp_err_t http_rest_with_url(esp_http_client_method_t method, void *url, 
                            char *post_data, int post_data_len,
                            char *response_buf, int response_buf_size,
                            int timeoutMs, http_event_handle_cb event_handle_cb)
{
    /**
     * NOTE: All the configuration parameters for http_client must be spefied either in URL or as host and path parameters.
     * If host and path parameters are not set, query parameter will be ignored. In such cases, query parameter should be specified in URL.
     *
     * If URL as well as host and path parameters are specified, values of host and path will be considered.
     */
	/*有些参数没有配置则会使用默认参数*/
    http_event_handle_cb http_handle_cb = event_handle_cb?event_handle_cb:_http_event_handler;
    esp_http_client_config_t config = {
		.url = url,
		.method = method,
        .event_handler = http_handle_cb,
        .user_data = response_buf,        // Pass address of local buffer to get response
        .buffer_size = response_buf_size,
        .buffer_size_tx = post_data_len,
        .timeout_ms = timeoutMs,
    };
    if(method == HTTP_METHOD_POST && post_data && post_data_len > 0)
    {
      config.buffer_size_tx += 256; //除了发送的数据，还有头部信息，所以需要加上256
      //ESP_LOGI(TAG, "post_data = %s",post_data);
    }
    esp_http_client_handle_t client = esp_http_client_init(&config);

    // 设置POST数据（当方法为POST时）
    if(method == HTTP_METHOD_POST && post_data && post_data_len > 0) {
        //esp_http_client_set_header(client, "Content-Type", "application/json");
        esp_http_client_set_header(client, "Content-Type", "application/x-www-form-urlencoded");
        esp_http_client_set_post_field(client, post_data, post_data_len);
    }

    // GET
    esp_err_t err = esp_http_client_perform(client);
    if (err == ESP_OK) {
        if(method == HTTP_METHOD_GET)
        {
            ESP_LOGI(TAG, "HTTP GET Status = %d, content_length = %lld",
                    esp_http_client_get_status_code(client),
                    esp_http_client_get_content_length(client));
        }
        else if(method == HTTP_METHOD_POST)
        {
            ESP_LOGI(TAG, "HTTP POST Status = %d",esp_http_client_get_status_code(client));
        }
    } else {
        ESP_LOGE(TAG, "HTTP request failed: %s", esp_err_to_name(err));
    }
    // ESP_LOG_BUFFER_HEX(TAG, local_response_buffer, strlen(local_response_buffer)); /*以十六进制形式输凿/
    //ESP_LOGI(TAG, "recv data ---2 = %d %s\r\n", strlen(local_response_buffer), local_response_buffer); //打印数据
  
    esp_http_client_cleanup(client);

    return err;
}


bool  get_timestamp_by_http()
{
    int nHttpBufSize = 1024;
    char *buf = (char *)malloc(nHttpBufSize);
    memset(buf,0,nHttpBufSize);

    char getServerTimeUrl[128]={0};
    sprintf(getServerTimeUrl,"%s/serverInfo/getServerTime",HTTP_SERVER_BASIC_ADDR);

    bool result = false;
    esp_err_t err = http_rest_with_url(HTTP_METHOD_GET,getServerTimeUrl,NULL,0,buf,nHttpBufSize-1,3000,NULL);
    if(err == ESP_OK)
    {
        cJSON *root = cJSON_Parse(buf);
        if(root != NULL)
        {
            cJSON *code = cJSON_GetObjectItem(root,"code");
            if (cJSON_IsNumber(code) && code->valueint == 200) {
                cJSON *data = cJSON_GetObjectItem(root, "data");
                //ESP_LOGI(TAG, "timestamp double = %f",data->valuedouble);
                sprintf(g_sys_timeStamp,"%.0f",data->valuedouble);
                ESP_LOGI(TAG, "http get timestamp = %s",g_sys_timeStamp);
                result = true;
            }
            cJSON_Delete(root);
        }
        else {
            ESP_LOGE(TAG, "Failed to parse JSON string");
        }
    }
    free(buf);
    return result;
}


bool  get_tcp_mqtt_server_info_by_http()
{
    int nHttpBufSize = 1024;
    char *buf = (char *)malloc(nHttpBufSize);
    memset(buf,0,nHttpBufSize);

    char getServerInfoUrl[128]={0};
    sprintf(getServerInfoUrl,"%s/serverInfo/getServerInfo",HTTP_SERVER_BASIC_ADDR);

    char deviceSecret[128]={0};
    char deviceCode_3st[4]={0};
    memcpy(deviceCode_3st,g_device_code,3);
    sprintf(deviceSecret,"%s%s%s",g_device_code,g_sys_timeStamp,deviceCode_3st);
    //ESP_LOGI(TAG, "deviceSecret = %s",deviceSecret);
    char md5Secret[37]={0};
    calculate_md5(deviceSecret,md5Secret);

    //取MD5的前10位
    char finalSecret[11]={0};
    memcpy(finalSecret,md5Secret,10);

    //ESP_LOGI(TAG,"finalSecret : %s", finalSecret); 
    
    #if 0
    //创建json对象
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root,"deviceCode",g_device_code);
    cJSON_AddStringToObject(root,"secret",finalSecret);
    cJSON_AddStringToObject(root,"ts",g_sys_timeStamp);

    //将json对象转换为字符串
    char *jsonStr = cJSON_Print(root);
    //检查 JSON 字符串是否生成成功
    if (!jsonStr) {
        ESP_LOGE(TAG, "Failed to create JSON string");
        goto cleanup; // 跳转到统一清理点
    }
    #endif

    bool result = false;
    // 创建urlencoded格式的POST数据
    char post_data[256];
    snprintf(post_data, sizeof(post_data), 
           "secret=%s&ts=%s&deviceCode=%s",
           finalSecret, 
           g_sys_timeStamp, 
           g_device_code);

    // 发送POST请求
    esp_err_t err = http_rest_with_url(HTTP_METHOD_POST, getServerInfoUrl, post_data, strlen(post_data), buf, nHttpBufSize-1, 3000, NULL);
 
    if(err == ESP_OK) {
        //ESP_LOGI(TAG, "http get server info = %s",buf);
        cJSON *resp = cJSON_Parse(buf);
        if (resp) {
            cJSON *code = cJSON_GetObjectItem(resp, "code");
            //ESP_LOGI(TAG, "http get server info code = %d",code->valueint);
            if (cJSON_IsNumber(code) && code->valueint == 200) {
                cJSON *data = cJSON_GetObjectItem(resp, "data");
                // 解析服务器返回的TCP/MQTT配置（需根据实际响应结构补充）
                if (data) {
                    // 解析MQTT服务器配置
                    cJSON *mqttSrv = cJSON_GetObjectItem(data, "mqttSrv");
                    cJSON *tcpSrv = cJSON_GetObjectItem(data, "tcpSrv");

                    if (cJSON_IsString(mqttSrv) && cJSON_IsString(tcpSrv)) {
                        // 分割服务器地址和端口
                        char *colon = strchr(mqttSrv->valuestring, ':');
                        if (colon) {
                            strncpy(g_mqtt_server, mqttSrv->valuestring, colon - mqttSrv->valuestring);
                            g_mqtt_port = atoi(colon + 1);
                        }
                        
                        colon = strchr(tcpSrv->valuestring, ':');
                        if (colon) {
                            strncpy(g_tcp_server, tcpSrv->valuestring, colon - tcpSrv->valuestring);
                            g_tcp_port = atoi(colon + 1);
                        }
                        
                        ESP_LOGI(TAG, "Got server config: MQTT[%s:%d], TCP[%s:%d]", 
                               g_mqtt_server, g_mqtt_port, g_tcp_server, g_tcp_port);
                        result = true;
                    }
                }
            }
            cJSON_Delete(resp);
        }
    }

    // 释放 JSON 字符串的内存
    //if (jsonStr) free(jsonStr);
    //cJSON_Delete(root);
    free(buf);
    return result;
}


static void get_timestamp_by_tcp()
{
    if(tcp_socket<0)
    {
        return;
    }

    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root,"deviceCode",g_device_code);
    //将json对象转换为字符串
    char *getServerTimeJson = cJSON_Print(root);

    unsigned int jsonLen = strlen(getServerTimeJson);
    controlTcpHeader getServerTimeHeader = {htonl(0x01020304), 0x01, 0x01, 0x19, 0x00000000, 0xff, htonl(jsonLen), htonl(jsonLen)};

    char *getServerTimeMessage = (char *)malloc(sizeof(getServerTimeHeader) + jsonLen + 1);
    memset(getServerTimeMessage, '\0', sizeof(getServerTimeHeader) + jsonLen + 1);
    memcpy(getServerTimeMessage, (const char *)&getServerTimeHeader, sizeof(getServerTimeHeader));
    memcpy(getServerTimeMessage + sizeof(getServerTimeHeader), (const char *)getServerTimeJson, jsonLen);


    ESP_LOGI(TAG,"send tcp get ts!");
    int senLen=send(tcp_socket,(unsigned char  *)getServerTimeMessage, sizeof(getServerTimeHeader) + jsonLen, 0);
    if (senLen < 0) {
        ESP_LOGE(TAG, "Send error: %s", strerror(errno));
    }

    if(getServerTimeJson)
        free(getServerTimeJson); 
    if(root)
        cJSON_Delete(root);
    free(getServerTimeMessage);
}


static void tcp_login()
{
    if(tcp_socket<0)
    {
        return;
    }

    cJSON *root = cJSON_CreateObject();

    char deviceSecret[128]={0};
    char deviceCode_3st[4]={0};
    memcpy(deviceCode_3st,g_device_code,3);
    sprintf(deviceSecret,"%s%s%s",g_device_code,g_sys_timeStamp,deviceCode_3st);
    //ESP_LOGI(TAG, "deviceSecret = %s",deviceSecret);
    char md5Secret[37]={0};
    calculate_md5(deviceSecret,md5Secret);

    //取MD5的前10位
    char finalSecret[11]={0};
    memcpy(finalSecret,md5Secret,10);

    cJSON_AddStringToObject(root,"deviceCode",g_device_code);
    cJSON_AddStringToObject(root,"password",finalSecret);
    cJSON_AddStringToObject(root,"ts",g_sys_timeStamp);
    cJSON_AddStringToObject(root,"ak","trumpetA");
    cJSON_AddStringToObject(root,"secret","longzyttrumpet");

    //将json对象转换为字符串
    char *loginJson = cJSON_Print(root);

    unsigned int jsonLen = strlen(loginJson);
    controlTcpHeader getServerTimeHeader = {htonl(0x01020304), 0x01, 0x01, 0x00, 0x00000000, 0xff, htonl(jsonLen), htonl(jsonLen)};

    char *loginMessage = (char *)malloc(sizeof(getServerTimeHeader) + jsonLen + 1);
    memset(loginMessage, '\0', sizeof(getServerTimeHeader) + jsonLen + 1);
    memcpy(loginMessage, (const char *)&getServerTimeHeader, sizeof(getServerTimeHeader));
    memcpy(loginMessage + sizeof(getServerTimeHeader), (const char *)loginJson, jsonLen);


    ESP_LOGI(TAG,"send tcp get ts!");
    int senLen=send(tcp_socket,(unsigned char  *)loginMessage, sizeof(getServerTimeHeader) + jsonLen, 0);
    if (senLen < 0) {
        ESP_LOGE(TAG, "Send error: %s", strerror(errno));
    }

    if(loginJson)
        free(loginJson); 
    if(root)
        cJSON_Delete(root);
    free(loginMessage);
}



static void tcp_client_task(void *pvParameters)
{
    unsigned char recv_header_buffer[TCP_PROTOCOL_HEADER_LEN];
    unsigned int recv_header_pos=0;
    struct sockaddr_in dest_addr;

    while (1) {

        //获取时间戳
        if(!get_timestamp_by_http())
        {
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            continue;
        }
        //获取TCP、mqtt服务器信息
        if(!get_tcp_mqtt_server_info_by_http())
        {
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            continue;
        }

        // 解析服务器地址（支持域名和IP）
        struct addrinfo hints = {
            .ai_family = AF_INET,
            .ai_socktype = SOCK_STREAM
        };
        struct addrinfo *res = NULL;
        
        int err = getaddrinfo(g_tcp_server, NULL, &hints, &res);
        if (err != 0 || res == NULL) {
            ESP_LOGE(TAG, "DNS lookup failed: %d", err);
            if (res) freeaddrinfo(res);
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            continue;
        }

         // 设置目标地址
        memcpy(&dest_addr, res->ai_addr, sizeof(struct sockaddr_in));
        dest_addr.sin_port = htons(g_tcp_port);
        freeaddrinfo(res);

        // 创建socket（使用全局变量）
        tcp_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_IP);
        if (tcp_socket < 0) {
            tcp_socket = -1;
            ESP_LOGE(TAG, "Unable to create socket: errno %d", errno);
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            continue;
        }

        //ESP_LOGI(TAG, "Connecting to %s:%d", TCP_SERVER_ADDR, TCP_SERVER_PORT);
        err = connect(tcp_socket, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
        if (err != 0) {
            close(tcp_socket); // 确保关闭之前的socket
            tcp_socket = -1;
            vTaskDelay(3000 / portTICK_PERIOD_MS);
            ESP_LOGE(TAG, "Socket unable to connect: errno %d", errno);
            continue;
        }
        ESP_LOGI(TAG, "Tcp Successfully connected");

        last_tcp_recv_time = xTaskGetTickCount(); // 添加计时器

        //发送登录请求
        tcp_login();


        while (1) {
            #if 0
            int len = recv(tcp_socket, rx_buffer, sizeof(rx_buffer) - 1, 0);
            // Error occurred during receiving
            if (len < 0 || len == 0) {
                ESP_LOGE(TAG, "recv failed: errno %d", errno);
                break;
            }
            ESP_LOGI(TAG, "TCP Received %d bytes", len>256?256:len);
            #endif

            // 接收头部
            int len = recv(tcp_socket, recv_header_buffer+recv_header_pos, 20-recv_header_pos, 0);
            if (len < 0 || len == 0) {
                ESP_LOGE(TAG, "tcp recv failed: errno %d", errno);
                break;
            }
            if(len<20 && recv_header_pos == 0)
            {
                recv_header_pos = len;
                ESP_LOGE(TAG, "tcp recv header failed: len %d", len);
                vTaskDelay(100 / portTICK_PERIOD_MS);
                continue;
            }
            else
            {
                recv_header_pos = 0;
            }
            // 解析头部
            controlTcpHeader *header = (controlTcpHeader *)recv_header_buffer;
            unsigned int jsonLen = ntohl(header->jsonLen);
            unsigned int totalLen = ntohl(header->totalLen);
            if (header->magic != htonl(0x01020304))
            {
                 //如果头部错误，则重新获取20字节
                 ESP_LOGE(TAG, "recv header error!");
                 continue;
            }
            unsigned char *recv_buffer = (unsigned char *)malloc(TCP_PROTOCOL_HEADER_LEN + totalLen);
            memset(recv_buffer, 0, TCP_PROTOCOL_HEADER_LEN + totalLen);
            memcpy(recv_buffer, recv_header_buffer, TCP_PROTOCOL_HEADER_LEN);
            
            int bytes_received = 0;
            bool bRecvError = false;
            while (bytes_received < totalLen)
            {
                int len = recv(tcp_socket, recv_buffer + TCP_PROTOCOL_HEADER_LEN + bytes_received, totalLen - bytes_received,0);
                if (len < 0 || len == 0) {
                    ESP_LOGE(TAG, "recv failed2: errno %d", errno);
                    bRecvError=true;
                    break;
                }
                bytes_received += len;
            }
            
            if(!bRecvError)
            {
                //打印messageType
                //ESP_LOGI(TAG, "TCP Received(Real) %d bytes, messageType = %d", totalLen, header->messageType);
                last_tcp_recv_time = xTaskGetTickCount();
                unsigned char *recvData = recv_buffer + TCP_PROTOCOL_HEADER_LEN;
                if(tcp_msg_handle(header->messageType,recvData,totalLen,jsonLen) == TcpHandleResult_TcpError)
                {
                    break;
                }
            }
            free(recv_buffer);
            continue;
        }
        ESP_LOGE(TAG, "TCP Connection closed");
        if (tcp_socket >= 0)
        {
            close(tcp_socket);
            tcp_socket = -1;
        }

        if(IS_SERVER_PLAY_MP3_SRC)
            cleanPlayTask(true);

        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    vTaskDelete(NULL);
}


char* construct_mqtt_device_info(const char * device_code,int play_status,int vol, char * prj_name,char * fw_ver, char * prj_ver) {
    cJSON *root = cJSON_CreateObject();
    cJSON *data = cJSON_CreateObject();
    cJSON_AddStringToObject(root,"event","clientInfo");
    cJSON_AddStringToObject(data,"imei",device_code);
    cJSON_AddNumberToObject(data,"playStatus",play_status);
    cJSON_AddNumberToObject(data,"vol",vol);
    cJSON_AddStringToObject(data,"prjName",prj_name);
    cJSON_AddStringToObject(data,"fwVer",fw_ver);
    cJSON_AddStringToObject(data,"prjVer",prj_ver);
    
    cJSON_AddItemToObject(root,"data",data);
    char *jsonStr = cJSON_Print(root);
 // 修复顺序：先删除父对象root，会自动删除子对象data
    cJSON_Delete(root);
    // 删除 data 会导致双重释放，因为data已经是root的子项
    // cJSON_Delete(data); // 这行需要删除

    return jsonStr;
}

static void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    esp_mqtt_event_handle_t event = event_data;
    switch (event->event_id) {
        case MQTT_EVENT_CONNECTED:
            ESP_LOGI(TAG, "MQTT Connected");
            g_mqtt_connected = true;
            
            //先发布上线消息
            char* json_device_info = construct_mqtt_device_info(g_device_code, 0, g_system_volume, PRJ_NAME, FIRMWARE_VERISON, PRJ_VER);
            esp_mqtt_client_publish(mqtt_client_handle, mqtt_pub_topic_name, json_device_info, strlen(json_device_info), 0, 0);
            free(json_device_info);
            
            //再订阅主题
            esp_mqtt_client_subscribe(mqtt_client_handle, mqtt_sub_topic_name, 0);

            break;
        case MQTT_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "MQTT Disconnected");
            g_mqtt_connected = false;
            break;
        case MQTT_EVENT_DATA:
        #if 0
            ESP_LOGI(TAG, "MQTT Message Received");
            // 处理收到的MQTT消息
            if (event->topic) {
                ESP_LOGI(TAG, "Topic=%.*s", event->topic_len, event->topic);
            }
            ESP_LOGI(TAG, "Data=%.*s", event->data_len, event->data);
        #endif
            mqtt_msg_handle(event->data,event->data_len);
            break;
        case MQTT_EVENT_ERROR:
            ESP_LOGE(TAG, "MQTT Error: %s", esp_err_to_name(event->error_handle->error_type));
            break;
        default:
            break;
    }
}

static void mqtt_client_task(void *pvParameters)
{
    // 添加连接等待机制
    int connect_retry_count = 0;
    while (1) {
        // 等待获取有效的服务器配置
        if (strlen(g_mqtt_server) == 0 || g_mqtt_port == 0) {
            vTaskDelay(2000 / portTICK_PERIOD_MS);
            continue;
        }

        // 配置MQTT客户端
        esp_mqtt_client_config_t mqtt_cfg = {
            .broker = {
                .address.uri = NULL,
                .address.hostname = g_mqtt_server,
                .address.port = g_mqtt_port,
                .address.transport = MQTT_TRANSPORT_OVER_TCP,    /* TCP模式 */
            },
            .session.keepalive = 40,  // 心跳间隔缩短为40秒
            .credentials = {
                .client_id = g_device_code
            }
        };

        // 初始化MQTT客户端
        if (!mqtt_client_handle) {
            mqtt_client_handle = esp_mqtt_client_init(&mqtt_cfg);
            esp_mqtt_client_register_event(mqtt_client_handle, ESP_EVENT_ANY_ID, mqtt_event_handler, NULL);
        }

        connect_retry_count = 0;

        // 启动连接
        esp_err_t err = esp_mqtt_client_start(mqtt_client_handle);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "MQTT Start failed: %s", esp_err_to_name(err));
            vTaskDelay(5000 / portTICK_PERIOD_MS);
            continue;
        }

        while (!g_mqtt_connected && connect_retry_count++ < 3) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }

        // 保持任务运行直到连接断开
        while (g_mqtt_connected) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
        }

        // 断开后清理并重试
        esp_mqtt_client_stop(mqtt_client_handle);
        esp_mqtt_client_destroy(mqtt_client_handle);
        mqtt_client_handle = NULL;
        vTaskDelay(5000 / portTICK_PERIOD_MS);
    }
    vTaskDelete(NULL);
}


// char数组转成SHORT类型
unsigned short CharsToShort(const unsigned char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    unsigned short	data = ((unsigned char)pData[0])*256 + (unsigned char)pData[1];

    return data;
}

// 获取校验码
unsigned char GetChecksum(const unsigned char*	data,			// 数据内容
                            int		nLen)			// 数据长度
{
    // 数据长度为0
    if (nLen <= 0)
    {
        return 0;
    }

    unsigned char checksum = data[0];

    for (int i = 1; i < nLen; ++i)
    {
        // 异或和
        checksum = (unsigned char)(checksum ^ data[i]);
    }

    return checksum;
}


static void soundCard_send_online()
{
    if(soundCard_tcp_socket<0)
    {
        return;
    }
    unsigned char *buf = (unsigned char *)malloc(256);
    memset(buf, 0, 256);

    buf[0]  = CMD_SOUNDCARD_CLIENT_ONLINE/256;					// 请求命令
    buf[1]  = CMD_SOUNDCARD_CLIENT_ONLINE%256;
    buf[2]  = 1;			// 包序号
    buf[3]  = 0x00;						// 保留位
    buf[4]  = MODEL_WIFI_SOUNDCARD_DEVICE;               // 设备型号
    buf[5]  = 0x00;						// 包属性

    int dataPos=8;
    buf[dataPos++] = strlen(g_device_code);
    memcpy(buf+dataPos, g_device_code, strlen(g_device_code));
    dataPos += strlen(g_device_code);
    buf[dataPos++] = 0; //设备名称长度为0
    buf[dataPos++] = g_playTaskSrc; //音源ID
    buf[dataPos++] = 0; //节目名称长度
    buf[dataPos++] = 0; //播放状态

    buf[dataPos] = GetChecksum(buf+8, dataPos-8);
    dataPos++;
     
    int dataLen=dataPos-8-1;

    buf[6]=dataLen/256;
    buf[7]=dataLen%256;

    send(soundCard_tcp_socket,(unsigned char  *)buf, dataPos, 0);
    free(buf);
}

static void soundCard_send_heartBeat()
{
   if(soundCard_tcp_socket<0)
    {
        return;
    }
    unsigned char *buf = (unsigned char *)malloc(256);
    memset(buf, 0, 256);

    buf[0]  = CMD_SOUNDCARD_CLIENT_HEARTBEAT/256;					// 请求命令
    buf[1]  = CMD_SOUNDCARD_CLIENT_HEARTBEAT%256;
    buf[2]  = 1;			// 包序号
    buf[3]  = 0x00;						// 保留位
    buf[4]  = MODEL_WIFI_SOUNDCARD_DEVICE;               // 设备型号
    buf[5]  = 0x00;						// 包属性

    int dataPos=8;

    buf[dataPos++] = strlen(g_device_code);
    memcpy(buf+dataPos, g_device_code, strlen(g_device_code));
    dataPos += strlen(g_device_code);

    buf[dataPos] = GetChecksum(buf+8, dataPos-8);
    dataPos++;
     
    int dataLen=dataPos-8-1;

    buf[6]=dataLen/256;
    buf[7]=dataLen%256;

    send(soundCard_tcp_socket,(unsigned char  *)buf, dataPos, 0);
    free(buf);
}

static void soundCard_tcpClient_task(void *pvParameters)
{
    struct sockaddr_in dest_addr;
    int recvBufferMaxLen=1100;
    //unsigned char recv_buffer[1100]={0};
    unsigned char recv_header_buffer[8];
    unsigned int recv_header_pos=0;

    while (1) {
        
        if(!(strlen(g_soundCard_server_Addr)>0 && g_soundCard_server_port>0))
        {
            vTaskDelay(pdMS_TO_TICKS(1000));
            continue;
        }

         // 设置目标地址,将g_soundCard_server_Addr给到dest_addr
        inet_pton(AF_INET, g_soundCard_server_Addr, &dest_addr.sin_addr);
        dest_addr.sin_port = htons(g_soundCard_server_port);
        dest_addr.sin_family = AF_INET;  // 确保设置了地址族

        // 创建socket（使用全局变量）
        soundCard_tcp_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_IP);
        if (soundCard_tcp_socket < 0) {
            soundCard_tcp_socket = -1;
            ESP_LOGE(TAG, "Unable to create socket: errno %d", errno);
            vTaskDelay(pdMS_TO_TICKS(3000));
            continue;
        }

        //ESP_LOGI(TAG, "Connecting to %s:%d", TCP_SERVER_ADDR, TCP_SERVER_PORT);
       esp_err_t err = connect(soundCard_tcp_socket, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
        if (err != 0) {
            close(soundCard_tcp_socket); // 确保关闭之前的socket
            soundCard_tcp_socket = -1;
            vTaskDelay(pdMS_TO_TICKS(3000));
            ESP_LOGE(TAG, "SoundCard Socket unable to connect: errno %d", errno);
            continue;
        }
        ESP_LOGI(TAG, "SoundCard TCP Successfully connected");

        last_soundCard_recv_time = xTaskGetTickCount(); // 添加计时器

        //发送上线信息
        soundCard_send_online();

        while (1) {
            // 接收头部
            int len = recv(soundCard_tcp_socket, recv_header_buffer+recv_header_pos, 8-recv_header_pos, 0);
            if (len < 0 || len == 0) {
                ESP_LOGE(TAG, "soundCard recv failed: errno %d", errno);
                break;
            }
            if(len<8 && recv_header_pos == 0)
            {
                recv_header_pos = len;
                ESP_LOGE(TAG, "soundCard recv header failed: len %d", len);
                vTaskDelay(100 / portTICK_PERIOD_MS);
                continue;
            }
            else
            {
                recv_header_pos = 0;
            }
            // 解析头部
            ushort  command     = CharsToShort(&recv_header_buffer[0]);        // 命令
            u_char	nReserved	= (unsigned char)recv_header_buffer[3];		// 保留字
            u_char	devModel	= (unsigned char)recv_header_buffer[4];		// 设备型号
            u_char	packAttr	= (unsigned char)recv_header_buffer[5];		// 包属性  3.15
            ushort	uDataLen	= CharsToShort(&recv_header_buffer[6]);		// 数据长度
            u_char	dataPos		= 8;

            if(uDataLen>=recvBufferMaxLen)
            {
                ESP_LOGI(TAG, "soundCard recv uDataLen error: %d", uDataLen);
                continue;
            }

            int reMaintotalLen = uDataLen + 1;

            unsigned char *recv_buffer = (unsigned char *)malloc(8 + reMaintotalLen);
            memset(recv_buffer, 0, 8 + reMaintotalLen);
            memcpy(recv_buffer, recv_header_buffer, 8);
            
            int bytes_received = 0;
            bool bRecvError = false;
            while (bytes_received < reMaintotalLen)
            {
                int len = recv(soundCard_tcp_socket, recv_buffer + 8 + bytes_received, reMaintotalLen - bytes_received,0);
                if (len < 0 || len == 0) {
                    ESP_LOGE(TAG, "recv failed2: errno %d", errno);
                    bRecvError=true;
                    break;
                }
                bytes_received += len;
            }
            
            if(!bRecvError)
            {
                if(recv_buffer[8+reMaintotalLen-1] != GetChecksum(recv_buffer+dataPos,uDataLen))
                {
                    ESP_LOGI(TAG, "soundCard recv checksum error!");
                    free(recv_buffer);
                    continue;
                }
                last_soundCard_recv_time = xTaskGetTickCount(); // 添加计时器
                soundCard_tcp_handle(command,&recv_buffer[dataPos],uDataLen);
            }
            free(recv_buffer);
            continue;
        }
        ESP_LOGE(TAG, "SoundCard TCP Connection closed");
        if (soundCard_tcp_socket >= 0)
        {
            close(soundCard_tcp_socket);
            soundCard_tcp_socket = -1;
        }

        if(g_playTaskSrc==TASK_SRC_SOUNDCARD)
        {
            cleanPlayTask(true);
        }

         vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    vTaskDelete(NULL);
}






static void network_event_task(void *arg) {
    uint32_t last_get_tcp_ts_time = 0;
    uint32_t last_soundCard_heartbeat_time = 0;  // 新增：记录上次心跳时间
    bool curAmpGpioStatus=false;
    while (1) 
    {
        if (tcp_socket >= 0) 
        {
            // 检查是否超过150秒没有收到数据
            if (last_tcp_recv_time>0 && ((xTaskGetTickCount() - last_tcp_recv_time) * portTICK_PERIOD_MS > 150000)) {
                ESP_LOGE(TAG, "TCP connection idle for 150 seconds, closing...");
                close(tcp_socket);
                tcp_socket = -1;
                // 清除播放任务
                //cleanPlayTask(true);
                last_tcp_recv_time = xTaskGetTickCount(); // 重置计时器
            }

            if(xTaskGetTickCount() * portTICK_PERIOD_MS - last_get_tcp_ts_time > 20000) 
            {
                last_get_tcp_ts_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
                get_timestamp_by_tcp();
            }
        }
        
        // 如果已经连接上声卡采集服务器，那么每隔10秒发送心跳包
        if(soundCard_tcp_socket >= 0)
        {
            // 检查是否超过150秒没有收到数据
            if (last_soundCard_recv_time>0 && ((xTaskGetTickCount() - last_soundCard_recv_time) * portTICK_PERIOD_MS > 60000)) {
                ESP_LOGE(TAG, "soundCard_tcp_socket idle for 60 seconds, closing...");
                close(soundCard_tcp_socket);
                soundCard_tcp_socket = -1;
                last_soundCard_recv_time = xTaskGetTickCount(); // 重置计时器
            }

            if(xTaskGetTickCount() * portTICK_PERIOD_MS - last_soundCard_heartbeat_time > 10000)
            {
                last_soundCard_heartbeat_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
                soundCard_send_heartBeat();
            }
        }

        if(g_playTaskSrc!=TASK_SRC_NONE && !curAmpGpioStatus)
        {
            curAmpGpioStatus=true;
            GpioSetAmpValid(true);
        }
        else if(g_playTaskSrc==TASK_SRC_NONE && curAmpGpioStatus)
        {
            curAmpGpioStatus=false;
            GpioSetAmpValid(false);
        }

        vTaskDelay(100 / portTICK_PERIOD_MS);
    }
}

void start_network_client_task()
{
    xTaskCreate(network_event_task, "network_event", 2048, NULL, 2, NULL);
    xTaskCreate(tcp_client_task, "tcp client", 4096, NULL, 4, NULL);
    xTaskCreate(mqtt_client_task, "mqtt client", 2048, NULL, 5, NULL);
    xTaskCreate(soundCard_tcpClient_task, "soundCard client", 4096, NULL, 5, NULL);
    //start_mp3Player();
}