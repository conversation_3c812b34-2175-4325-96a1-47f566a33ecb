/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:02:08 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-16 15:06:56
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_wifi.h"
#include "esp_eth.h"

#include "netTools.h"

// TODO: 引用外部模块
// #include "../system/sysMethod.h"
// #include "../sigmastar/mi_functions.h"
#include "sysconf.h"

static const char *TAG = "NETTOOLS";


/*added by xyang for ip check 2011-08-13*/
int if_a_string_is_a_valid_ipv4_address(const char *str)
{
    struct in_addr addr;
    int ret;
    if( strcmp(str,"0.0.0.0") == 0 )
    {
        return 0;
    }
    ret = inet_pton(AF_INET, str, &addr);
    if (ret > 0);
    else if (ret < 0)
    {
        //printf("inet_pton error");
    }
    else
    {
        //printf("\"%s\" is not a valid IPv4 address\n", str);
    }
    return ret;
}

/**
 * [isValidSubnetMask 判断子网掩码是否正确]
 * @param  mask [description]
 * @return      [description]
 */
int isValidSubnetMask(const char *mask){
    if(if_a_string_is_a_valid_ipv4_address (mask))
    {
        unsigned int b = 0, i, n[4];
        sscanf(mask, "%u.%u.%u.%u", &n[3], &n[2], &n[1], &n[0]);
        for(i = 0; i < 4; ++i) //将子网掩码存入32位无符号整型
            b += n[i] << (i * 8);
        b = ~b + 1;
        if((b & (b - 1)) == 0)   //判断是否为2^n
            return 1;
    }
    return 0;
}

/**
 * [isGatewayByNetmask_Error 判断ip、子网掩码、网关是否正确]
 * @param  ip   [description]
 * @param  mask [description]
 * @param  gw   [description]
 * @return      [0:正确 -1：ip错误  -2：子网掩码错误 -3：网关错误 -4：ip和网关不在同一网段内 ]
 */
int isGatewayByNetmask_Error(const char *ip,const char *mask,const char *gw)
{
    if(!if_a_string_is_a_valid_ipv4_address (ip)){
        return -1;
    } 
    if(!isValidSubnetMask(mask)){
        return -2;
    }
    if(!if_a_string_is_a_valid_ipv4_address (gw)){
        return -3;
    }

    unsigned long addr=0; 
    unsigned long gwi=0; //网关
    unsigned long netMask=0;
    inet_pton(AF_INET, ip, &addr);
    inet_pton(AF_INET, gw, &gwi);
    inet_pton(AF_INET, mask, &netMask);

    if ((addr & netMask) != (gwi & netMask))
    {
        return -4;
    }

	//找到最后一个/
	char *lastDotPos=strrchr(gw,'.');
	if(lastDotPos)
	{
        if( strcmp(lastDotPos+1,"0") == 0 || strcmp(lastDotPos+1,"255") == 0 )
        {
            printf("isGatewayByNetmask_Error:gw error!\n");
            return -5;
        }
	}
    return 0;
}




// 常见顶级域名（TLD）列表
static const char *common_tlds[] = {
    ".com", ".org", ".net", ".io", ".co", ".ai", ".gov", ".edu", ".mil", ".int",
    ".biz", ".info", ".mobi", ".name", ".tv", ".cc", ".me", ".us", ".uk", ".ca",
    ".au", ".de", ".jp", ".cn", ".in", ".br", ".fr", ".ru", ".es", ".it", ".nl",
    ".se", ".ch", ".no", ".mx", ".ar", ".pl", ".id", ".kr", ".tr", ".th", ".vn",
    ".gr", ".be", ".at", ".dk", ".fi", ".nz", ".pt", ".sg", ".my", ".hk", ".tw",
    ".il", ".ir", ".pk", ".ph", ".ro", ".ua", ".za", ".eg", ".sa", ".ae", ".ng",
    ".ke", ".ma", ".cl", ".pe", ".co.uk", ".org.uk", ".gov.uk", ".ac.uk", ".edu.au",
    ".co.jp", ".ne.jp", ".or.jp", ".go.jp", ".ac.jp", ".com.cn", ".net.cn", ".org.cn",
    ".gov.cn", ".edu.cn", ".com.br", ".net.br", ".org.br", ".gov.br", ".edu.br",
    NULL  // 结束标志
};

// 检查字符串是否以某个 TLD 结尾
bool ends_with_tld(const char *domain) {
    if (domain == NULL || strlen(domain) < 3 || strlen(domain) > 63) {
        return false;  // 最短的 TLD 是 2 字符（如 .co），但通常至少 3（.com）
    }

    for (int i = 0; common_tlds[i] != NULL; i++) {
        const char *tld = common_tlds[i];
        size_t tld_len = strlen(tld);
        size_t domain_len = strlen(domain);

        if (domain_len >= tld_len) {
            if (strcmp(domain + domain_len - tld_len, tld) == 0) {
                return true;
            }
        }
    }

    return false;
}

// 改进后的域名校验（增加 TLD 检查）
bool is_valid_domain(const char *domain) {
    if (domain == NULL || strlen(domain) > 63 || strlen(domain) == 0) {
        return false;
    }

    // 不能以点或连字符开头或结尾
    if (domain[0] == '.' || domain[0] == '-' || 
        domain[strlen(domain) - 1] == '.' || domain[strlen(domain) - 1] == '-') {
        return false;
    }

    const char *ptr = domain;
    int label_len = 0;

    while (*ptr) {
        if (*ptr == '.') {
            if (label_len == 0 || label_len > 63) {
                return false;
            }
            label_len = 0;
            ptr++;
            continue;
        }
        if (!isalnum((unsigned char)*ptr)) {
            if (*ptr != '-') {
                return false;
            }
        }
        label_len++;
        ptr++;
    }

    // 最后一个标签不能为空或过长
    if (label_len == 0 || label_len > 63) {
        return false;
    }

    // 检查是否以常见 TLD 结尾
    return ends_with_tld(domain);
}