idf_component_register(SRCS "network_process.c" "udp_client.c"
                            "tcp_client.c"
                            "multicast.c"
                            "netTools.c"
                            "recv_stream.c"
                       INCLUDE_DIRS "."
                       REQUIRES lwip esp_timer esp_netif esp_wifi esp_eth driver freertos main)

# 添加编译选项以忽略指针符号警告
target_compile_options(${COMPONENT_LIB} PRIVATE
    -Wno-error=pointer-sign
    -Wno-error=char-subscripts
    -Wno-error=incompatible-pointer-types
    -Wno-error=sizeof-pointer-memaccess
    -Wno-pointer-sign
)