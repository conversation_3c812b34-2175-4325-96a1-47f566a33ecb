#ifndef ___CH390D_DRIVER_H__
#define ___CH390D_DRIVER_H__


#include "ch390.h"
#include "esp_eth_mac_ch390.h"
#include "esp_eth_phy_ch390.h"


#define ETH_MASTER_INT_GPIO       9//-1
#define ETH_MASTER_MOSI_GPIO      2
#define ETH_MASTER_SCLK_GPIO      3
#define ETH_MASTER_CS_GPIO        4
#define ETH_MASTER_MISO_GPIO      5

#define SPI_MASTER_HOST           1 //SPI1:0,SPI2:1,SPI3:2
#define SPI_MASTER_CLOCK_MHZ      5


void ch390d_driver_init(void);

#endif

