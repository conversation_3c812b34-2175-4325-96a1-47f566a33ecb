/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:18:53 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-06 17:35:29
 */

#ifndef _MULTICAST_H_
#define _MULTICAST_H_

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"

extern struct sockaddr_in multi_host_addr;

void Multicast_Pkg_Process(unsigned char *pkg_buf, int pkg_len);
void send_online_info(void);
void start_multicast_cmd_client(void);
void multicast_send_data(unsigned char * data,unsigned short len);
void Send_Unonline_Info(void);
void power_on_notice_host(void);
#endif