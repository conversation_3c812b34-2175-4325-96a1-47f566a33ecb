/* Esptouch example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
#include "esp_eap_client.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_smartconfig.h"
#include "esp_mac.h"
#include "nvs.h"
#include "driver/gpio.h"
#include "esp_wifi_types.h"

#include "ch390d_driver.h"

#include "sysconf.h"

#define GPIO_RESET_KEY_PIN     9
#define KEY_LONG_PRESS_MS   5000
#define GPIO_STATUS_LED_PIN    4

#define NVS_NAMESPACE_WIFI  "wifi_config"
#define SSID_KEY       "ssid"
#define PASSWORD_KEY   "password"

/* FreeRTOS event group to signal when we are connected & ready to make a request */

/* The event group allows multiple bits for each event,
   but we only care about one event - are we connected
   to the AP with an IP? */

static const char *TAG = "wifiSpeaker";

wifi_config_t wifi_config = {0};

bool s_wifi_connected = false;

// 在全局变量区域新增
static bool s_has_wifi_config = false;
typedef enum {
    LED_OFF,
    LED_ON,
    SLOW_BLINK,
    FAST_BLINK
} led_mode_t;
static led_mode_t current_led_mode = LED_OFF;

static void gpio_init();
static void start_network();


void start_mp3Player_url_task(char *url);

#if IS_RELEASE_VERSION
#include "driver/usb_serial_jtag.h"
bool EnableUsbGpio = true;
#else
bool EnableUsbGpio = false;
#endif


void start_esp_httpServer_task(void);

void GpioSetAmpValid(bool isValid)
{
    gpio_set_level(GPIO_NUM_10, !isValid);
}


//GPIO输出网络连接状态(音乐信号闪烁)
void GPIO_OutPut_LedStatusFlicker(int outPut)
{
    gpio_set_level(GPIO_NUM_1, outPut);
}

#if IS_RELEASE_VERSION
static void usb_switch_to_gpio() {
    // 关闭USB JTAG模块
    usb_serial_jtag_driver_uninstall();
    
    // 配置GPIO18和GPIO19为普通IO
    gpio_reset_pin(GPIO_NUM_18);
    gpio_reset_pin(GPIO_NUM_19);
    gpio_set_direction(GPIO_NUM_18, GPIO_MODE_OUTPUT);
    gpio_set_direction(GPIO_NUM_19, GPIO_MODE_OUTPUT);

    gpio_set_level(GPIO_NUM_18, 0);
    gpio_set_level(GPIO_NUM_19, 0);

    //ESP_LOGI(TAG, "USB JTAG disabled, GPIO18/19 now available as GPIO.");
}
#endif

// 初始化GPIO
void gpio_init()
{
    gpio_install_isr_service(0);
    #if 0
    // 复位GPIO
    gpio_config_t gpio_reset_conf = {
        .pin_bit_mask = (1ULL << GPIO_RESET_KEY_PIN),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };

    // LED指示灯GPIO
    gpio_config_t gpio_status_led_conf = {
        .pin_bit_mask = (1ULL << GPIO_STATUS_LED_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };

    gpio_config(&gpio_reset_conf);
    gpio_config(&gpio_status_led_conf);
    #endif

    // 功放GPIO
    gpio_config_t gpio_status_amp_conf = {
        .pin_bit_mask = (1ULL << GPIO_NUM_10),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    gpio_config(&gpio_status_amp_conf);
    GpioSetAmpValid(false);

    // LED GPIO
    gpio_config_t gpio_led_status_conf = {
        .pin_bit_mask = (1ULL << GPIO_NUM_1),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    gpio_config(&gpio_led_status_conf);
    GPIO_OutPut_LedStatusFlicker(0);
}

// 复位按钮、LED响应控制任务
static void button_led_task(void *arg) {
    // 按钮检测部分
    uint32_t press_start = 0;
    // LED控制部分
    bool led_state = false;
    uint32_t last_led_change = 0;

#if 0
    uint32_t last_usb_status_change = 0;
    bool usb_gpio = false;
#endif
    while (1) {
        #if 0
        if(EnableUsbGpio)
        {
            if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_usb_status_change >= 1000) {
                usb_gpio =!usb_gpio;
                gpio_set_level(GPIO_NUM_18, usb_gpio);
                gpio_set_level(GPIO_NUM_19, usb_gpio);
                last_usb_status_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
            }
        }
        #endif


        // 按钮检测逻辑
        if (gpio_get_level(GPIO_RESET_KEY_PIN) == 0) {
            if (press_start == 0) {
                press_start = xTaskGetTickCount() * portTICK_PERIOD_MS;
            } else if ((xTaskGetTickCount() * portTICK_PERIOD_MS - press_start) >= KEY_LONG_PRESS_MS) {
                // 长按处理
                nvs_handle_t handle;
                nvs_open(NVS_NAMESPACE_WIFI, NVS_READWRITE, &handle);
                nvs_erase_all(handle);
                nvs_commit(handle);
                nvs_close(handle);
                esp_restart();
            }
        } else {
            press_start = 0;
        }

        // LED控制逻辑
        switch(current_led_mode) {
            case LED_ON:
                gpio_set_level(GPIO_STATUS_LED_PIN, 1);
                break;
            case LED_OFF:
                gpio_set_level(GPIO_STATUS_LED_PIN, 0);
                break;
            case SLOW_BLINK:
                if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_led_change > 1000) {
                    led_state = !led_state;
                    gpio_set_level(GPIO_STATUS_LED_PIN, led_state);
                    last_led_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
                }
                break;
            case FAST_BLINK:
                if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_led_change > 250) {
                    led_state = !led_state;
                    gpio_set_level(GPIO_STATUS_LED_PIN, led_state);
                    last_led_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
                }
                break;
        }

        vTaskDelay(50 / portTICK_PERIOD_MS); // 统一检测周期
    }
}


void hostOnlineCheck_task(void *arg)
{
	while(1)
	{
		if(network_init_flag)
	    {
          if( host_ready_offline_flag == 1  && g_host_device_TimeOut == 0 )
          {
            host_ready_offline_flag = 0;
            //打开网络连接状态IO输出
			//GPIO_OutPut_Server_Connection(1);
          }
          
          if(g_host_device_TimeOut>=0)
          {
            g_host_device_TimeOut++;
            if(g_host_device_TimeOut >= HOST_TIMEOUT_VALUE)
            {
              ESP_LOGI(TAG,"Host offline1:%d\r\n", g_host_device_TimeOut);

              if( g_media_source != SOURCE_NET_PAGING && g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX && g_media_source != SOURCE_100V_INPUT )
              {
              }
              
              if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status())
              {
                  tcp_client_reconnect();
              }

              g_host_device_TimeOut=-1; //离线
              host_ready_offline_flag = 1;
            }
          }
	    }
		vTaskDelay(pdMS_TO_TICKS(1000));
	}
}

//创建IP检测线程
void ip_check_task(void *arg)
{
    unsigned char link_status_change_cnt=0;
	bool reLinked_flag=false;
    unsigned char temp_link_status=eth_link_status;
    bool net_init_flag=false;
    while(1)
    {
        //uxTaskGetStackHighWaterMark()返回的是任务栈的最小剩余空间，
        //这个值会记录栈使用的历史最大深度。一旦栈被深度使用过，这个高水位标记就不会自动恢复。
        //unsigned int highWaterMark = uxTaskGetStackHighWaterMark(NULL);
        //ESP_LOGI(TAG, "Stack remaining: %d bytes", highWaterMark);

        vTaskDelay(pdMS_TO_TICKS(100));
        if(!net_init_flag)
        {
            if(network_init_flag)
            {
                net_init_flag=true;
                temp_link_status=eth_link_status;
                //创建网络线程
                start_network_data_process_pthread();
                start_udp_client();
                start_multicast_cmd_client();

                if(g_network_mode == NETWORK_MODE_WAN)
                {
                    TCP_Client_Start();
                }
                else
                {
                    send_online_info();
                }
            }
        }
        if(!net_init_flag)
        {
            continue;
        }

        if(temp_link_status!=eth_link_status)
        {
            link_status_change_cnt++;
            if(link_status_change_cnt>1)
            {
                link_status_change_cnt=0;
                temp_link_status=eth_link_status;
                printf("eth_link_status=%d\n",eth_link_status);
                if(eth_link_status == 1)
                {
                    //如果WAN模式下已经连接，需要立即重连
                    if(g_network_mode == NETWORK_MODE_WAN && tcp_get_master_connect_status() == 1)
                    {
                        tcp_client_reconnect();
                    }
                    reLinked_flag=true;
                }
                else
                {
                    reLinked_flag=false;

                    #if 0
                    if(g_signal_100v)   //网线拔出后，如果100V存在，那么切换过去
                    {
                        Ctrl_Relay_100V(1);	//音频继电器归位，此处先进行切换，避免Set_zone_idle_status占用时间
                        Set_zone_idle_status(NULL,  __func__, __LINE__,true);
                    }
                    #endif
                    if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-15 )
                    {
                        g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-15;
                        host_ready_offline_flag = 1;
                        //关闭网络连接状态IO输出
                        //GPIO_OutPut_Server_Connection(0);
                    }
                }
            }
        }
        else
        {
            link_status_change_cnt=0;
        }
    }
}


//网络连接状态LED变化线程(音乐信号闪烁，用于P26、P28)
void Create_LedStatus_Event(void *p_arg)
{
  while(1)
  {
      //0-未连接上服务器 1-连接上服务器 2-有音乐信号

      static int count = 0;
      static int prev_state=-1,state=0;
      static int state_has_changed = 0; // 变态是否已经改变
      static int led_status=0;   //默认灭
      unsigned char sysSource = get_system_source();
      if(!IS_SERVER_CONNECTED)
      {
          state = 0;
      }
      else if(sysSource!=SOURCE_NULL)
      {
          state = 2;
      }
      else
      {
          state = 1;
      }
  
      if (prev_state != state) {
          prev_state = state;
          count = 0;
          state_has_changed=1;
      }
      else
      {
          state_has_changed=0;
      }

      if (state == 0 && state_has_changed) {
        GPIO_OutPut_LedStatusFlicker(0);
        led_status=0;
      }
      else if (state == 1 && state_has_changed) {
          //printf("LED常亮\n");
          GPIO_OutPut_LedStatusFlicker(1); // LED常亮
          led_status=1;
      }
      // 如果状态为正在播放，则LED亮0.3秒，灭0.3秒
      else if (state == 2) {
          if (count < 3) {
              if(!led_status)
              {
                  //printf("LED亮0.3秒\n");
                  GPIO_OutPut_LedStatusFlicker(1); // 亮0.3秒
                  led_status=1;
              }
          } else {
              if(led_status)
              {
                  //printf("LED灭0.3秒\n");
                  GPIO_OutPut_LedStatusFlicker(0); // 灭0.3秒
                  led_status=0;
              }
          }
          count = (count + 1) % 6; // 6个时间单位为一次循环，每个时间单位100ms，所以6个单位为0.6秒
      }

      usleep(100000);
  }
}

// 修改app_main函数
void app_main(void) {

    #if IS_CLOSE_USB_UART_LOG
    esp_log_level_set("*", ESP_LOG_NONE);
    #else
    esp_log_level_set("*", ESP_LOG_INFO);
    #endif

    #if IS_RELEASE_VERSION
    if(EnableUsbGpio)
    {
        usb_switch_to_gpio();
    }
    #endif

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    read_sysconf(INI_SETCION_NETWORK,NULL);
	read_sysconf(INI_SECTION_DEVICE,NULL);
	read_sysconf(INI_SECTION_BASIC,NULL);
	read_sysconf(INI_SETCION_MFR,NULL);
	read_sysconf(INI_SETCION_DSP_Firmware,NULL);

    // 初始化GPIO
    gpio_init();

    //初始化ch390d
    ch390d_driver_init();

    // 初始化播放
    init_netPlay();

#if 0
    //网络连接状态LED变化线程(音乐信号闪烁)
    //xTaskCreate(button_led_task, "button_led_task", 2048, NULL, 1, NULL);
	Create_LedStatus_Event_Task();
    //IP检测线程
    HOSTIP_Check_THREAD();
#endif

    xTaskCreate(hostOnlineCheck_task, "hostOnlineCheck", 2048, NULL, 1, NULL); //创建主机在线检测线程
    xTaskCreate(ip_check_task, "ip_check", 2560, NULL, 2, NULL);    //创建IP检测线程
    xTaskCreate(Create_LedStatus_Event, "LedStatus", 2048, NULL, 1, NULL);    //创建网络连接状态LED变化线程(音乐信号闪烁)


    start_esp_httpServer_task();

    unsigned int cnt_100ms=0;
    while(1)
    {
        cnt_100ms++;
        if( (++cnt_100ms)%10 == 0 )
        {
            sysRunTime++;
            if( (sysRunTime)%10 == 0 )	//每隔10s发送一次上线通知
			{	
                if(network_init_flag)
                {
				    send_online_info();
                }
			}
            if(IS_SERVER_CONNECTED && !HasGotSysTime)
                requset_Host_Synchroniztion_Time();

            //print_free_memory();
        }
        //延时100ms
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}



static void start_network()
{
    //start_mp3Player_url_task("http://************:9999/Data/Program/Other/1KHz-0dB_60s_new.mp3");
    start_network_client_task();
}