#ifndef _SYS_CONFIG_H
#define _SYS_CONFIG_H

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include "const.h"
#include "sysMethod.h"
#if 1
#include "network.h"
#include "netHandle.h"

#endif

#include "network_protocol.h"
#include "multicast.h"
#include "udp_client.h"
#include "tcp_client.h"
#include "network_process.h"
#include "priority.h"
#include "netPlay.h"

#if 1   //无用
extern char g_device_code[64];
extern char g_sys_timeStamp[16];
extern char mqtt_sub_topic_name[96];
extern char g_mqtt_server[64];
extern int g_mqtt_port;
extern char g_tcp_server[64];
extern int g_tcp_port;

extern bool s_wifi_connected;
extern char g_soundCard_server_Addr[64];    //声卡服务器地址
extern short int g_soundCard_server_port;         //声卡服务器端口
#endif


extern unsigned char g_system_volume;




enum
{
	DSP_AUDIO_MODULE_ADC0_L,					//AMIC0
	DSP_AUDIO_MODULE_ADC0_R,					//AMIC1
	DSP_AUDIO_MODULE_ADC1_L,					//AMIC2
	DSP_AUDIO_MODULE_ADC1_R,					//None
	DSP_AUDIO_MODULE_DAC0_L,					//DAC0_L
	DSP_AUDIO_MODULE_DAC0_R,					//DAC0_R
	DSP_AUDIO_MODULE_BT,						//BT(NONE)
	DSP_AUDIO_MODULE_MAX,                    
};

typedef struct {
	unsigned char module_switch[DSP_AUDIO_MODULE_MAX];	//模块开关
	unsigned short module_gain[DSP_AUDIO_MODULE_MAX];	//模块增益
}st_dsp_firmware_feature_info;
extern st_dsp_firmware_feature_info dsp_firmware_feature;


typedef struct
{
	int	sec;		/* Seconds: 0-59 (K&R says 0-61?) */
	int	min;		/* Minutes: 0-59 */
	int	hour;	/* Hours since midnight: 0-23 */
	int	day;	/* Day of the month: 1-31 */
	int	mon;		/* Months *since* january: 0-11 */
	int	year;	/* Years since 1900 */
	int	weekday;	/* Days since Sunday (0-6) */
}_stMyTime;

extern _stMyTime st_CurrentTime;

extern unsigned int sysRunTime;	//系统运行时间（秒）
extern unsigned char g_sysBootType;	//系统启动类型（1：开门狗重启 2：系统复位 3：断电重启) ,默认为3
extern unsigned char power_on_flag; //仅上电首次通知主机标志

/***************网络相关定义 START **************/
extern char g_ipAddress[16];					//IP地址
extern char g_subnet_mask[16];				//子网掩码
extern char g_gateway[16];					//网关
extern unsigned char g_mac_addr[6];   //MAC ADDR
extern unsigned char network_init_flag;	   //网络初始化完成标志

extern unsigned char g_network_mode;	//网络模式
extern char g_host_tcp_addr_domain[64];	//主机地址(域名或者IP)
extern char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP
extern unsigned short g_host_tcp_port;				    //主机TCP端口
extern unsigned short g_host_kcp_port;        		        //KCP端口

extern int g_current_connect_tcp_id;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
extern char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
extern unsigned short g_host_tcp_port2;				//主机TCP端口2
extern unsigned short g_host_kcp_port2;        		//KCP端口2

extern unsigned char eth_link_status;				//有线网卡连接状态

/*****IP属性 ************/
extern unsigned char  g_IP_Assign;	//IP分配方式(static or DHCP)
extern char g_Static_ip_address[32];		//静态IP地址
extern char g_Subnet_Mask[32];				//子网掩码
extern char g_GateWay[32];					//网关
extern char g_Primary_DNS[32];				//主DNS服务器
extern char g_Alternative_DNS[32];			//备用DNS服务器

/***************网络相关定义 END **************/

/**************工作模式相关定义 START************/
extern unsigned char g_system_work_mode;
/**************工作模式相关定义 END************/

/**********设备信息START*****************/
extern char g_device_alias[128];	//设备别名
/*********设备信息END******************/

/**********基础信息START****************/
extern unsigned char g_system_volume;	//系统音量
extern unsigned char g_pre_system_volume;		//系统前一音量
extern unsigned char g_sub_volume;		//子音量
extern unsigned char g_volumeCD_aux_volume;			//音控器本地音量(存在时才有效)
extern unsigned char g_volumeCD_net_volume;			//音控器网络音量(存在时才有效)
extern unsigned char g_aux_volume;			//本地音量
extern unsigned char g_lineVolume;	//线路音量
extern unsigned char g_micVolume;	//麦克风音量
extern unsigned char g_timing_volume;            //定时音量（记录正在定时播放时调节的音量）

extern signed char host_ready_offline_flag;			   // 主机即将离线标志
extern signed char g_host_device_TimeOut;            //主机离线计数,-1代表已经离线

extern unsigned int g_paging_status;		   // 寻呼状态
extern unsigned int g_media_source;		   // 音源状态
extern unsigned char g_media_name[128];//当前节目名称
extern unsigned char g_media_status;	//媒体播放状态

extern unsigned char g_terminal_control_mode;	//程控 手控0x10

extern unsigned char concentrate_repeat_paly_enable; //是否允许播放集中模式下重新请求播放音乐
extern unsigned char paging_repeat_again_enable;     //是否允许重新寻呼
extern unsigned char mixed_source_repeat_again_enable;     //是否允许重新进入混音音源
extern unsigned char phone_gateway_source_repeat_again_enable;     //是否允许重新进入电话网关音源

extern unsigned char g_signal_100v;   			//100V信号
extern unsigned char g_signal_aux;   //Aux信号

extern unsigned char g_system_language;		//系统语言

extern char g_device_serialNum[20];    	//设备序列号
extern char g_device_sn[32];            //设备SN

extern unsigned short  g_device_moduleId;	//设备型号ID
extern int g_Is_tcp_real_internet;					//TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP）


/*******集中模式变量************/
extern unsigned char g_concentrated_song_type;	//集中模式-歌曲类型	1为MP3 2为WAV
extern unsigned int  g_concentrated_song_sample_rate;	//集中模式-歌曲采样率
extern unsigned int  g_concentrated_song_fmt;	//集中模式-歌曲采样精度
extern unsigned int  g_concentrated_song_channels;	//集中模式-歌曲声道数
extern unsigned char g_concentrated_multicast_address[16];	//集中模式-组播地址
extern unsigned int  g_concentrated_multicast_port;		//集中模式-组播端口
extern unsigned int g_concentrated_need_exit_flag;		//集中模式 退出标志 0-不需要  1-需要退出
extern unsigned char g_concentrated_start;				//集中模式启动标志	
extern unsigned char g_concentrated_playing;		    //集中模式歌曲正在播放
extern int g_centralized_mode_timeout_count;			//集中模式传输超时计数
extern int g_centralized_mode_timing_repeat;         //集中模式传输超时重复次数，连续5次代表确实收不到组播数据，应该停止重发
extern int g_centralized_mode_timeout_pause;			//集中模式播歌超时检测暂停标志
extern int g_centralized_mode_is_existLocalSong;		//集中模式是否存在本地歌曲
extern bool g_IsCentralized_mode_multicast_new_cmd;		//集中模式组播播放是否采用新的命令
/*******集中模式变量************/

/*******音频采集变量************/
extern unsigned char g_collector_run_flag;                //音频采集器运行标志
extern unsigned int g_ac_sample_rate;				//音频采集器采样率
extern unsigned int g_ac_channels;					//音频采集器通道数
extern unsigned char g_ac_multicast_address[16];		//音频采集-组播地址
extern unsigned int g_ac_mcast_port;					//音频采集器的组播端口
extern unsigned char g_ac_source_id;					//音频采集器音源ID
extern unsigned char g_ac_source_priority;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）
/*******音频采集变量************/

/*******系统时间变量************/
extern unsigned char sys_date_buf[20];	//系统日期
extern unsigned char sys_time_buf[20];		//系统时间
extern unsigned char HasGotSysTime;		     //是否已经获取到系统时间
/*******系统时间变量************/
extern unsigned char g_allow_localSource;				//是否允许本地音源，收到在线音源指令时将其置于0，定时计数恢复，避免收到播放指令，还有短暂本地音乐



void save_sysconf(char *section,char *key);
void read_sysconf(char *section,char *key);
void format_sysconf();

void GpioSetAmpValid(bool isValid);


#define IS_SERVER_PLAY_MP3_SRC    (g_playTaskSrc == TASK_SRC_SERVER_MUSIC || g_playTaskSrc == TASK_SRC_CONTROLER_MUSIC || g_playTaskSrc == TASK_SRC_WEATHER || g_playTaskSrc == TASK_SRC_ALARM)


#define IS_SERVER_CONNECTED (g_host_device_TimeOut!=-1)
#endif