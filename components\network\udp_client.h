/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:18:27 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-06 17:10:39
 */

#ifndef _UDP_CLIENT_H_
#define _UDP_CLIENT_H_

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"

extern struct sockaddr_in udp_host_addr,udp_pager_addr,udp_networktools_addr;
extern struct sockaddr_in tmp_udp_host_addr,tmp_udp_pager_addr,tmp_udp_networktools_addr;

void start_udp_client(void);
void host_udp_send_data(unsigned char * data,unsigned short len);
void pager_udp_send_data(unsigned char * data,unsigned short len);

#endif