/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:30:07 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-24 18:09:29
 */
#include <semaphore.h>
#include <time.h>
#include "network_protocol.h"
#include "sysconf.h"
#include "udp_client.h"
#include "network_process.h"
#include "multicast.h"
#include "netTools.h"
#include "recv_stream.h"
#include "netPlay.h"
#include "ota_updater.h"
//#include "../kcp/mkcp.h"

static const char *TAG = "NETWORK_PROCESS";

_stNetworkRecv stNetworkRecv;

// 定义互斥锁和信号量句柄
static SemaphoreHandle_t mutex_netPkg = NULL;     // 替换为FreeRTOS互斥锁
static SemaphoreHandle_t sem_netPkgRecv = NULL;   // 信号量句柄

void NetPkg_Add(int NetType,unsigned char *buf,unsigned int len)
{
    if (len > MAX_BUF_SIZE || buf == NULL) return;
    
    // 获取互斥锁（阻塞等待）
    if (xSemaphoreTake(mutex_netPkg, portMAX_DELAY) == pdTRUE) {
		//ESP_LOGI(TAG,"NetPkg_Add:NetType=%d,len=%d",NetType,len);
        memcpy(stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].buf, buf, len);
        stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].len = len;
        stNetworkRecv.stNetworkBuf[stNetworkRecv.write_pos].type = NetType;
        
        // 环形缓冲区更新
        if (++stNetworkRecv.write_pos >= MAX_BUF_NUM) {
            stNetworkRecv.write_pos = 0;
        }
        
        // 释放信号量（通知接收任务）
        xSemaphoreGive(sem_netPkgRecv);
        
        // 释放互斥锁
        xSemaphoreGive(mutex_netPkg);
    }
}



/*********************************************************************
 * @fn      network_data_process
 *
 * @brief   网络数据处理
 *
 * @param   void
 *
 * @return	void
 */
void network_data_process(void *arg)
{
	while(1)
	{
		//等待sem_netPkgRecv
		xSemaphoreTake(sem_netPkgRecv, portMAX_DELAY);

		if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_SERVER || 
			stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_PAGER ||
			stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_TCP_SERVER	)
		{
			int device_model = stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf[4];

			if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_SERVER)
			{
				if(device_model == DEVICE_MODEL_HOST)
				{
					udp_host_addr=tmp_udp_host_addr;
					//printf("DEVICE_MODEL_HOST1：%d.%d.%d.%d\n", (udp_host_addr.sin_addr.s_addr)&0xff, (udp_host_addr.sin_addr.s_addr>>8)&0xff,(udp_host_addr.sin_addr.s_addr>>16)&0xff,(udp_host_addr.sin_addr.s_addr>>24)&0xff);
				}
				else if(device_model == DEVICE_MODEL_NETWORK_TOOLS)
				{
					udp_networktools_addr=tmp_udp_networktools_addr;
				}
			}
			else if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_UNICAST_PAGER)
			{
				if(device_model == DEVICE_MODEL_PAGING)
				{
					udp_pager_addr=tmp_udp_pager_addr;
				}
			}

			network_pkg_process(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].len);
		}
		else if(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].type == NET_TYPE_MULTICAST_SERVER)
		{
			Multicast_Pkg_Process(stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].buf,stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos].len);
		}

		memset(&stNetworkRecv.stNetworkBuf[stNetworkRecv.read_pos], 0, sizeof(_stNetworkBuf));

		stNetworkRecv.read_pos++;
		if(stNetworkRecv.read_pos >= MAX_BUF_NUM )
		{
			stNetworkRecv.read_pos = 0;
		}
	}
	vTaskDelete(NULL);
}

/*********************************************************************
 * @fn      start_network_data_process_pthread
 *
 * @brief   启动网络数据处理线程
 *
 * @param   void
 *
 * @return	void
 */
void start_network_data_process_pthread(void)
{
	// 创建互斥锁
    mutex_netPkg = xSemaphoreCreateMutex();
    
    // 创建计数信号量（最大计数=MAX_BUF_NUM，初始0）
    sem_netPkgRecv = xSemaphoreCreateCounting(MAX_BUF_NUM, 0);

	xTaskCreate(network_data_process, "network_data_process", 4096, NULL, 10, NULL);
}


/*********************************************************************
 * @fn      Calculate_XorDat
 *
 * @brief   计算数据包异或校验和
 *
 * @param   Data - 校验数据
 *			Length - 校验数据长度
 *
 * @return  Xordata - 校验和
 */
unsigned char Calculate_XorDat(unsigned char *Data, int Length)
{
	int i;
	unsigned char Xordata = 0;

	for (i = 0; i < Length; i++)
	{
		Xordata ^= Data[i];
	}

	return Xordata;
}


/****************************************************
 * @fn      Network_Send_Compose_CMD
 *
 * @brief   //发送命令组合
 *
 * @param  unsigned char *sendBuf -发送包指针,unsigned int cmd - 命令字,unsigned char deviceType - 设备类型,unsigned char Datalen-数据负载长度,unsigned char *Data 数据包
 *
 * @return	int 应答包长度
 */

int Network_Send_Compose_CMD(unsigned char *sendBuf,unsigned int cmd,unsigned char deviceType,int Datalen,unsigned char *Data)
{
	int i;
	int SendLen;
	memset(sendBuf,0,sizeof(sendBuf));
	sendBuf[0]=cmd>>8;		//命令字高位
	sendBuf[1]=cmd;			//命令字低位
	sendBuf[2]=0;			//包序号
	sendBuf[3]=0;			//保留
	sendBuf[4]=deviceType;	//设备型号
	sendBuf[5]=0;			//包属性4bit&编码格式4bit
	sendBuf[6]=Datalen>>8;
	sendBuf[7]=Datalen;
	for(i=0;i<Datalen;i++)
	{
		sendBuf[8+i]=Data[i];
	}
	sendBuf[8+Datalen]=Calculate_XorDat(Data,Datalen);

	SendLen=9+Datalen;

	return SendLen;
}



/*********************************************************************
 * @fn      respond_null_payload_pkg
 *
 * @brief   空负载应答包
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_null_payload_pkg(unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[128]={0};
	unsigned char client;
	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	client = rxbuf[4];
	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	if (client == DEVICE_MODEL_HOST || client == DEVICE_MODEL_NETWORK_TOOLS)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}




/*********************************************************************
 * @fn      HOST_CONCENTRATED_PLAY_SOURCE
 *
 * @brief   主机通知终端播放节目源（集中模式）
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 *********************************************************************/
void HOST_CONCENTRATED_PLAY_SOURCE(unsigned char *pkg_data)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len = 1;
	data[0] = 1;	//1-接受  2-拒绝
	int sendLen = 0;
	// 发送数据
	unsigned char client=pkg_data[4];

	g_allow_localSource=0;


	if(g_system_work_mode != WORK_MODE_CONCENTRATED)
	{
		g_system_work_mode = WORK_MODE_CONCENTRATED;
		Set_zone_idle_status(NULL,  __func__, __LINE__,true);
	}
	
	//需要确认是否进入播放
	int play_type = pkg_data[3];
	printf("play_type:%d\n", play_type);
	if(play_type == 1)  //控制设备或其它原因使分区变成停止, 而且该分区有播放任务，变为空闲状态，主机会发送命令确认（其它设备停止该分区或者意外停止）
	{
		if(!concentrate_repeat_paly_enable)			//如果不允许播放集中模式下再次请求播放，比如用寻呼台控制停止
		{   
		  	printf("--concentrate_repeat_paly_enable==0--\n");
		 	data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}


   	int payload_length = (pkg_data[6]<<8)+pkg_data[7];
	int pos = PAYLOAD_START;
	int play_source = pkg_data[pos++];

	int priority_isOK = 0;	//优先级检测标志
	int priority=0xff;
    printf("[HOST_CONCENTRATED_PLAY] source = 0x%02x\n", play_source);
	switch(play_source)
	{
		case 1:	//歌曲点播 
			priority=PRIORITY_NET_PLAY;
				if(PriorityIsValid(PRIORITY_NET_PLAY))
					priority_isOK=1;
			break;
		case 2:	//歌曲定时 
			priority=PRIORITY_TIMING;
				if(PriorityIsValid(PRIORITY_TIMING))
					priority_isOK=1;
			break;
		#if 0
		case 3:	//钟声 
			priority=PRIORITY_RING;
				if(PriorityIsValid(PRIORITY_RING) )
					priority_isOK=1;
			break;
		#endif
		case 4: //消防告警
			priority=PRIORITY_FIRE_ALARM;
			if(PriorityIsValid(PRIORITY_FIRE_ALARM) )
				priority_isOK = 1;
			break;
		case 5://监控事件
			priority=PRIORITY_MONITOR_EV;
            break;
	}
	if(PriorityIsValid(priority) && !(g_signal_100v && priority<PRIORITY_100V) ) //满足优先级要求，但100V信号存在时且目的优先级小于100V时不进入（消防告警除外）
	{
		priority_isOK = 1;
	}


	if(!priority_isOK)				//不满足优先级要求，不能拒绝，以便主机下次继续重发指令
	{   
		#if LOCAL_SOURCE_PRIORITY_HIGHEST
		//如果是本地音源，那么返回接受
		if(get_system_source() == SOURCE_AUX)
		{
			data[0] = 1;//返回接受
		}
		else
		#endif
		{
			if(play_type == 1)
				data[0] = 1;//返回接受
			else
			{
				data[0] = 2;//返回拒绝
			}
		}
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}

        	
	int t_concentrated_song_type = pkg_data[pos++];
   	int t_concentrated_song_sample_rate = (pkg_data[pos]<<16)+(pkg_data[pos+1]<<8)+(pkg_data[pos+2]);   //3个字节
   	pos += 3;
   	int t_concentrated_song_fmt = pkg_data[pos++];
   	int t_concentrated_song_channels = pkg_data[pos++];

   	//判断参数是否正常，否则返回
   	int parameter_true = 1;
   	if( !( t_concentrated_song_type == 1 || t_concentrated_song_type == 2 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_sample_rate == 8000 || t_concentrated_song_sample_rate == 11025 || t_concentrated_song_sample_rate == 12000 ||
		t_concentrated_song_sample_rate == 16000 || t_concentrated_song_sample_rate == 22050 || t_concentrated_song_sample_rate == 24000 ||
		 t_concentrated_song_sample_rate == 32000 || t_concentrated_song_sample_rate == 44100 || t_concentrated_song_sample_rate == 48000 ||
		  t_concentrated_song_sample_rate == 88200 || t_concentrated_song_sample_rate == 96000 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_fmt == 16 || t_concentrated_song_fmt == 24 || t_concentrated_song_fmt == 32 ) )
   	{
	 	parameter_true=0;
   	}
   	if( !( t_concentrated_song_channels == 1 || t_concentrated_song_channels == 2 ) )
   	{
	 	parameter_true=0;
   	}

   	if(t_concentrated_song_type == 0&&
		t_concentrated_song_sample_rate == 0&&
		t_concentrated_song_fmt == 0&&
		t_concentrated_song_channels == 0)
   	{
   		parameter_true =1;
   	}

	if(parameter_true == 0)
	{
        printf("parameter error,reject!\n");
		data[0] = 2;
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
		return;
	}

	if(t_concentrated_song_type == 0 &&
		t_concentrated_song_sample_rate == 0 &&
		t_concentrated_song_fmt == 0 &&
		t_concentrated_song_channels == 0)
	{
		return;
	}

   	g_centralized_mode_timeout_count = 0;//超时计数
        
	Clean_All_Audio();

	g_concentrated_song_type = t_concentrated_song_type;//歌曲类型
	g_concentrated_song_sample_rate = t_concentrated_song_sample_rate;//采样率
	g_concentrated_song_fmt = t_concentrated_song_fmt;//采样精度
	g_concentrated_song_channels = t_concentrated_song_channels;//声道数


	int song_name_length = pkg_data[pos++];
	memset(g_media_name,0,sizeof(g_media_name));
	memcpy(g_media_name,pkg_data+pos,song_name_length);
	pos+=song_name_length;
	int multicast_length=pkg_data[pos++];
	memset(g_concentrated_multicast_address,0,sizeof(g_concentrated_multicast_address));
	memcpy(g_concentrated_multicast_address,pkg_data+pos,multicast_length);

	pos += multicast_length;
	g_concentrated_multicast_port = (pkg_data[pos]<<8)+pkg_data[pos+1];
	pos += 2;
	
	printf("g_concentrated_multicast_address:%s\n", g_concentrated_multicast_address);
	printf("g_concentrated_multicast_port:%d\n", g_concentrated_multicast_port);
	printf("g_concentrated_song_type:%d\n", g_concentrated_song_type);
	printf("Song Name:%s\n", g_media_name);

	int tmp_media_source=SOURCE_LOCAL_PLAY;
	switch(play_source)
	{
		case 1:						//本地播放
			tmp_media_source = SOURCE_LOCAL_PLAY;
			break;
		case 2:						//定时
			tmp_media_source = SOURCE_TIMING;
			break;
		#if 0
		case 3:						//钟声
			tmp_media_source = SOURCE_RING;
			break;
		#endif
		case 4:						//消防告警
			tmp_media_source = SOURCE_FIRE_ALARM;
			break;
		case 5:						//监控事件
			tmp_media_source = SOURCE_MONITOR_EV;
			break;
	}


	if( !set_system_source(tmp_media_source) )
	{
		data[0] = 1;
     	sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
     	host_udp_send_data(sendBuf, sendLen);
		return;
	}

	int timing_vol = pkg_data[pos++];

	//保存音量
	if(play_source == 1)//本地播放
	{
		//20210122 增加音量恢复，避免定时完成后服务器立刻下发恢复本地播放，导致音量没有恢复
		if(timing_vol>=0 && timing_vol<=100)
		{
			g_system_volume=timing_vol;
		}
		else
		{
			g_system_volume=g_pre_system_volume;
		}
	}
	else if(play_source == 2)//定时
	{
		//定时前保存系统音量
		if(timing_vol>=0 && timing_vol<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
        {
			g_system_volume=timing_vol;
        }
		if( g_timing_volume != -1 )
		{
			g_system_volume = g_timing_volume;
		}
	}
	else if(play_source == 3)//钟声
	{

	}
	else if(play_source == 4)//消防告警
	{
		if(timing_vol>=0 && timing_vol<=100)
		{
			g_system_volume=timing_vol;
		}
		else
		{
			g_system_volume = FIRE_ALARM_DEFAULT_VOL;	//设置音量
		}
	}
	else if( play_source == 5)//监控事件
	{
		//监控事件前保存系统音量
		g_pre_system_volume=g_system_volume;
		if(timing_vol>=0 && timing_vol<=100)
			g_system_volume=timing_vol;
	}

	//记录总帧数,新版本服务器程序才支持 20220601
	g_centralized_mode_is_existLocalSong=0;

	g_IsCentralized_mode_multicast_new_cmd=false;

	if( pos < payload_length + PAYLOAD_START )
	{
		int fileLength=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int totalFramesCnt=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int currentFrame=(pkg_data[pos]<<24)+(pkg_data[pos+1]<<16)+(pkg_data[pos+2]<<8)+pkg_data[pos+3];
		pos+=4;
		int fileMd5Len=pkg_data[pos++];
		char fileMd5[32+1]={0};
		if(fileMd5Len>0 && fileMd5Len<=32)
		{
			memcpy(fileMd5,pkg_data+pos,fileMd5Len);
			pos+=fileMd5Len;
		}

		printf("fileLength=%d,totalFramesCnt=%d,currentFrame=%d\n",fileLength,totalFramesCnt,currentFrame);

		pos++;	//跳过一个Timer Volume

		if( pos < payload_length + PAYLOAD_START )
		{
			//如果有新的字段，从这里进入
			g_IsCentralized_mode_multicast_new_cmd=pkg_data[pos++];
			pos++;	//跳过一个Timer Volume
		}
	}

	data[0] = g_centralized_mode_is_existLocalSong?0x03:0x01;
    sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONCENTRATED_PLAY_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
    host_udp_send_data(sendBuf, sendLen);

	g_concentrated_start = 1;
    concentrate_repeat_paly_enable = 1;  //允许重新请求播放

	//重新计时
	g_centralized_mode_timeout_count = 0;
	g_centralized_mode_timeout_pause = 0;		//继续检测
	g_concentrated_need_exit_flag = 0;

    g_media_status = SONG_PLAYING;

	#if 1	//增加此段语句是为了在单播循环模式下，服务器能够记录歌曲变化。
	unsigned char t_media_name[128] = {0};
	strcpy(t_media_name,g_media_name);
	memset(g_media_name,0,sizeof(g_media_name));
	pkg_query_current_status(NULL);
	strcpy(g_media_name,t_media_name);
	#endif

	pkg_query_current_status(NULL);

    struct in_addr centralized_ipAdder;
    centralized_ipAdder.s_addr = inet_addr(g_concentrated_multicast_address); 
    
	//初始化ringbuf
	init_audio_buf();

	if(ip_addr_ismulticast(&centralized_ipAdder) && g_network_mode == NETWORK_MODE_LAN)       //组播地址才进行组播接收，否则KCP接收
    {
      	start_concentrated_recv_pthread();
    }
	start_mp3Player_stream_task();
}



/*********************************************************************
 * @fn      HOST_SET_AUDIO_COLLECOR_SOURCE
 *
 * @brief   //主机向音频采集器/终端设置音频采集音源
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_SET_AUDIO_COLLECOR_SOURCE(unsigned char *pkg_data)
{
	g_allow_localSource=0;
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=1;
	int sendLen=0;
	// 发送数据
	unsigned char client=pkg_data[4];
	unsigned char play_type=pkg_data[3];

	if(play_type == 1)  //控制设备或其它原因使分区变成停止, 而且该分区有播放任务，变为空闲状态，主机会发送命令确认（其它设备停止该分区或者意外停止）
	{
		if(!concentrate_repeat_paly_enable)			//如果不允许播放集中模式下再次请求播放，比如用寻呼台控制停止
		{   
		  	printf("--Audio collector:concentrate_repeat_paly_enable==0--\n");
		 	data[0]=2;		//拒绝
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
			host_udp_send_data(sendBuf, sendLen);
			return;
		}
	}

       
	printf("HOST_SET_AUDIO_COLLECOR_SOURCE.......\n");
	int valid = 1;

	//判断采样率信息是否正确
	int pos = PAYLOAD_START+4;
	int t_sample_rate = (pkg_data[pos]<<16)+(pkg_data[pos+1]<<8)+(pkg_data[pos+2]);   //3个字节
	pos += 3;
	int sample_fmt = pkg_data[pos++];
	int channels = pkg_data[pos++];
	int audioCodecs = pkg_data[pos++];

	int Payload_Length = (int)pkg_data[PAYLOAD_START-2]*256 + (int)pkg_data[PAYLOAD_START-1]; // 负载数据长度
	if(Payload_Length < 9)  //没有附带采样率信息，当成16K处理
	{
		t_sample_rate = 16000;
		sample_fmt = 16;
		channels = 1;
	}
	else if(! ( t_sample_rate == 16000 || t_sample_rate == 22050 || t_sample_rate == 32000 || t_sample_rate == 44100 || t_sample_rate == 48000 ) )
	{
		valid=0;
		printf("sample_rate:%d error\n",t_sample_rate);
	}
	else	//继续判断采样精度、声道、解码算法
	{
		if( !(sample_fmt == 16 && (channels == 1 || channels == 2) ) )
		{
			valid=0;
			printf("sample_fmt:%d,channels:%d error\n",sample_fmt,channels);
		}
	}

	//保存组播端口跟采集器音源id
	int temp_ac_mcast_port=(pkg_data[PAYLOAD_START]<<8)+pkg_data[PAYLOAD_START+1];
	int temp_ac_source_id=pkg_data[PAYLOAD_START+2];
	int audio_channel=pkg_data[PAYLOAD_START+3];
	temp_ac_source_id+=audio_channel-1;

	unsigned char isTiming = 0;
	unsigned char isTriger = 0;

	int priority=1;	//音频采集音源优先级（1-默认，低于定时，2-高优先级，高于定时）

	int timing_trigger_volume=g_system_volume;

	if( pos < Payload_Length + PAYLOAD_START )
	{
		//是否定时
		isTiming = pkg_data[pos++];
		//音量
		int volume = pkg_data[pos++];
		printf("AudioCollector:isTiming=%d,volume=%d\n",isTiming,volume);

		if(isTiming)
		{
			if(volume>=0 && volume<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
			{
				timing_trigger_volume=volume;
			}
		}
		
		if( pos < Payload_Length + PAYLOAD_START )
		{
			//是否触发
			isTriger = pkg_data[pos++];
			//音量
			volume = pkg_data[pos++];
			printf("AudioCollector:isTrigger=%d,volume=%d\n",isTriger,volume);

			if(!isTiming && isTriger)
			{
				if(volume>=0 && volume<=100)    //0xff跟随系统音量，其余音量需要保存上一次音量
				{
					timing_trigger_volume=volume;
				}
			}
		}
		if( pos < Payload_Length + PAYLOAD_START )
		{
			//优先级
			priority=pkg_data[pos++];
		}
	}

	//当前是高优先级采集音源，新的是低优先级采集音源，不处理。
	if(get_current_Priority() == PRIORITY_AUDIO_HIGH_PRIORITY && priority == 1)
	{
		return;
	}

	g_ac_source_priority = priority;

	bool ac_source_equal=false;
	if(valid)
	{
		#if (IS_DEVICE_DECODER_TERMINAL || IS_DEVICE_INTERCOM_TERMINAL || IS_DEVICE_AUDIO_MIXER)
		if( get_system_source() ==  temp_ac_source_id)
		{
			ac_source_equal=true;
		}
		#endif
		if( !set_system_source(temp_ac_source_id) )
		{
			valid=0;
		}
	}

	if(!valid)
	{
		data[0]=2;
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
        printf("----------refuse 0x02-----------\n");
		return;
	}
	else
	{
		//20220721,如果是定时采集，那么应答result=3
		if(isTiming)
		{
			data[0]=3;
		}
		//20230914,如果是触发采集，那么应答result=4
		else if(isTriger)
		{
			data[0]=4;
		}
		else
		{
			data[0]=1;
		}
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_AUDIO_COLLECOR_SOURCE,CURRENT_DEVICE_MODEL,data_len,data);
		host_udp_send_data(sendBuf, sendLen);
        printf("----------accept 0x01-----------\n");

		if(ac_source_equal)
		{
			return;
		}
	}


	//音频采集时集中模式播放歌曲时拒绝接收
  	concentrate_repeat_paly_enable = 1;

	//保存组播端口跟采集器音源id
	g_ac_mcast_port=temp_ac_mcast_port;
	g_ac_source_id=temp_ac_source_id;


	//清空节目名称
	memset(g_media_name,0,sizeof(g_media_name));
	pkg_query_current_status(NULL);
	g_ac_channels = channels;
	printf("g_ac_mcast_port=%d,g_ac_source_id=0x%x\n",g_ac_mcast_port, g_ac_source_id);


	Clean_All_Audio();

	g_system_volume=timing_trigger_volume;

	g_collector_run_flag = 1;
	g_ac_sample_rate = t_sample_rate;

	printf("g_ac_sample_rate:%d, sample_fmt:%d, g_ac_channels:%d\n", g_ac_sample_rate, sample_fmt, g_ac_channels);

	//初始化ringbuf
	init_audio_buf();

	//开启接收线程
	if( g_network_mode == NETWORK_MODE_LAN )       //组播地址才进行组播接收，否则KCP接收
    {
      	start_concentrated_recv_pthread();
    }
	start_mp3Player_stream_task();
}



/*********************************************************************
 * @fn      requset_Host_Synchroniztion_Time
 *
 * @brief   请求主机同步时间
 *
 * @param   void
 *
 * @return  none
 */
void requset_Host_Synchroniztion_Time(void)
{
	printf("requset_Host_Synchroniztion_Time...\n");
	unsigned char sendBuf[MAX_BUF_SIZE] = {0};
	unsigned char data[128] = {0};
	int data_len = 0;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SYN_HOST_TIME,CURRENT_DEVICE_MODEL,data_len,data);

	host_udp_send_data(sendBuf, sendLen);
}

/*********************************************************************
 * @fn      respond_pkg_time_synchronization
 *
 * @brief   向主机应答时间同步情况
 *
 * @param   rxbuf - 接收缓存
 *          code - 应答状态码
 *
 * @return	void
 *********************************************************************/
void respond_pkg_time_synchronization(unsigned char *rxbuf, unsigned char code)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	send_buf[PAYLOAD_START] = code;
	payloadSize += 1;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];

	host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
}

/*********************************************************************
 * @fn      pkg_set_local_time
 *
 * @brief   设置本地时间日期
 *
 * @param   rxbuf--接收缓冲区
 *
 * @return	void
 *********************************************************************/
void pkg_set_local_time(unsigned char *rxbuf)
{
	int i;
	int payloadSize = 0;
	int data_len = 0;
	unsigned char time_buf[32];
	unsigned char date_buf[32];

	// 初始化缓存
	memset(date_buf, 0x00, sizeof(date_buf));
	memset(time_buf, 0x00, sizeof(time_buf));

	// 接收日期数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 10)
	{
		respond_pkg_time_synchronization(rxbuf, TIME_FORMAT_ERROR);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			date_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		payloadSize += data_len;
		memcpy(sys_date_buf, date_buf, data_len);
	}

	// 接收时间数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 8)
	{
		respond_pkg_time_synchronization(rxbuf, TIME_FORMAT_ERROR);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			time_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		memcpy(sys_time_buf, time_buf, data_len);
		
		respond_pkg_time_synchronization(rxbuf, TIME_SET_SUCCEED);
	}

	HasGotSysTime=1;

	//检测是否需要写入
	int year=0,mon=0,day=0;
	int hour=0,min=0,sec=0;
	sscanf(date_buf,"%d-%d-%d",&year,&mon,&day);
	sscanf(time_buf,"%d:%d:%d",&hour,&min,&sec);
	int update_time_flag=1;
	//printf("year=%d,mon=%d,day=%d,hour=%d,min=%d,sec=%d\n",year,mon,day,hour,min,sec);

	if( st_CurrentTime.year == year && st_CurrentTime.mon == mon && st_CurrentTime.day == day )
	{
		int time_host=hour*3600+min*60+sec;
		int time_current = st_CurrentTime.hour*3600+st_CurrentTime.min*60+st_CurrentTime.sec;
		if( abs(time_host-time_current) <=3 )
		{
			update_time_flag=0;
		}
	}
	if(update_time_flag)
	{
		struct tm tmp_time;
		char date_time_buf[48]={0};
		sprintf(date_time_buf,"%s %s",sys_date_buf,sys_time_buf);
		strptime(date_time_buf,"%Y-%m-%d %H:%M:%S",&tmp_time); //时间24时制
		int timeStack = mktime(&tmp_time);
		struct timeval tnow;
		tnow.tv_sec = timeStack;

		settimeofday(&tnow,NULL);

		printf("Set LocalTime:%s %s\n",date_buf,time_buf);
	}
	else
	{
		printf("time not change\n");
	}
}

/*********************************************************************
 * @fn      Change_Device_Alias
 *
 * @brief   修改设备别名
 *
 * @param   rxbuf--数据缓冲区
 *
 * @return  none
*********************************************************************/
void Change_Device_Alias(unsigned char *rxbuf)
{
	// 应答
	respond_null_payload_pkg(rxbuf);
	int i;
	int alias_len = 0;
	// 判断别名长度是否大于0
	if (rxbuf[PAYLOAD_START] == 0)
	{
		return;
	}
	else
	{
		alias_len = rxbuf[PAYLOAD_START]; // 获取别名长度
	}
	// 获取别名并保存
	memset(g_device_alias, 0x00, sizeof(g_device_alias));
	for (i = 0; i < alias_len; i++)
	{
		g_device_alias[i] = rxbuf[PAYLOAD_START+1+i];
	}
	send_online_info();
	save_sysconf(INI_SECTION_DEVICE,"Device_Name");//主机修改设备别名保存
	printf("g_device_alias:%s\n", g_device_alias);
}

/*********************************************************************
 * @fn      pkg_query_current_status
 *
 * @brief   主机查询本机当前状态(音量、节目源名称、播放状态)
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_query_current_status(unsigned char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE] = {0};
	int i=0;
	unsigned char data[128] = {0};
	int data_len = 0;
	unsigned char client;
	if(rxbuf != NULL)
		client = rxbuf[4];

	if(g_paging_status == PAGING_START)
	{
		data[0] = pager_property.volume;
	}
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
	{
		data[0] = appData.voiceInfoSettings.callVolumeTemp;
	}
	#endif
	else
	{
		data[0] = g_system_volume;
	}
        
	data[1] = g_media_source;		//音源
	data[2] = g_media_status;	//播放状态
	//本地播放与网络点播
	int media_name_len=0;
	if(g_media_source == SOURCE_LOCAL_PLAY ||g_media_source == SOURCE_FIRE_ALARM || g_media_source == SOURCE_TIMING || g_media_source == SOURCE_MONITOR_EV)
	{
		media_name_len = strlen(g_media_name) > 64 ? 64 : strlen(g_media_name);
	}
	data[3] = media_name_len;
	for(i = 0; i < media_name_len; i++)
	{
		data[4+i] = g_media_name[i];
	}
	data[4+media_name_len] = g_centralized_mode_is_existLocalSong?2:1;
	data_len = 3 + 1 + media_name_len + 1;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_GET_ZONE_DETAIL_INFO,CURRENT_DEVICE_MODEL,data_len,data);
	if(rxbuf == NULL)
	{
		//组播发送
		multicast_send_data(sendBuf, sendLen);
		#if ENABLE_TCP_CLIENT
		if(g_network_mode == NETWORK_MODE_WAN)
			host_tcp_send_data(sendBuf,sendLen);
		#endif
	}
	else
	{
		if (client == DEVICE_MODEL_HOST)
		{
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			#if ENABLE_TCP_CLIENT
			if(g_network_mode == NETWORK_MODE_WAN)
				host_tcp_send_data(sendBuf,sendLen);
			#endif
		}
		else if (client == DEVICE_MODEL_PAGING)
		{
			pager_udp_send_data(sendBuf, sendLen);
		}
	}
}


/*********************************************************************
 * @fn      respond_pkg_query_volume
 *
 * @brief   应答音量查询
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_pkg_query_volume(unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];
	if(g_paging_status == PAGING_START)
		send_buf[PAYLOAD_START] = pager_property.volume;
	#if SUPPORT_SIP
	else if(g_media_source == SOURCE_SIP_CALLING)
		send_buf[PAYLOAD_START] = appData.voiceInfoSettings.callVolumeTemp;
	#endif
	else
		send_buf[PAYLOAD_START] = g_system_volume;
	payloadSize += 1;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}

/*********************************************************************
 * @fn      pkg_set_volume
 *
 * @brief   主机设置或查询本机音量
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_set_volume(unsigned char *rxbuf)
{
	int i = 0, cunt = 0;
	int temp_volume = 0;
	switch (rxbuf[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
			respond_pkg_query_volume(rxbuf);
			break;
		case CMD_SET : // 设置
			respond_null_payload_pkg(rxbuf);
			if(g_paging_status == PAGING_START)
			{       
                                
				pager_property.volume = rxbuf[PAYLOAD_START+1];
				pkg_query_current_status(NULL);

				printf("set g_paging_vol:%d\n", pager_property.volume);
			}
			#if SUPPORT_SIP
			else if(g_media_source == SOURCE_SIP_CALLING)	
			{
				appData.voiceInfoSettings.callVolumeTemp = rxbuf[PAYLOAD_START+1];
				sem_sip_conf_send();
				pkg_query_current_status(NULL);
			}
			#endif
			else
			{
				temp_volume = g_system_volume;
				g_system_volume = rxbuf[PAYLOAD_START+1];
				pkg_query_current_status(NULL);
				//if(g_system_volume != 0)
				g_pre_system_volume = g_system_volume;

				//如果是定时音源，那么记录此音量
				if( get_system_source() == SOURCE_TIMING)
				{
					g_timing_volume = g_system_volume;
				}
                                
				if(temp_volume == g_system_volume)
				{
					break;
				}
				save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
#if 0	//20221223 4G复位测试，实测复位后可能RDNIS异常，重启系统后可以正常工作。故使用复位后，需要重启一遍系统确保网卡正常。
				if(g_system_volume == 0)
				{
					GPIO_OutPut_Module4G_Reset(1);
					usleep(200000);
					GPIO_OutPut_Module4G_Reset(0);
				}
				else if(g_system_volume == 10)
				{
					GPIO_OutPut_Module4G_POWER(1);
					usleep(1600*1000);    //此处开机，需要大于1.2秒，小于1.5秒，否则当模块处于开机状态下，可能会关机
					GPIO_OutPut_Module4G_POWER(0);
				}
#endif
			}
         
			break;
		default :
			break;
	}
}



/*********************************************************************
 * @fn      respond_pkg_query_sub_volume
 *
 * @brief   应答子音量查询
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_pkg_query_sub_volume(unsigned int SetOrQuery,unsigned char *rxbuf)
{
	int payloadSize = 0;
	unsigned char send_buf[MAX_BUF_SIZE];

	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = RESERVE;
	// 产品型号
	send_buf[4] = CURRENT_DEVICE_MODEL;
	// 包属性及编码格式
	send_buf[5] = rxbuf[5];

	send_buf[PAYLOAD_START] = SetOrQuery;
	send_buf[PAYLOAD_START+1] = g_sub_volume;
	send_buf[PAYLOAD_START+2] = g_aux_volume;
	payloadSize = 3;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	// 发送数据
	unsigned char client=rxbuf[4];
	if (client == DEVICE_MODEL_HOST)
	{
		// 发送数据
		host_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
	else if (client == DEVICE_MODEL_PAGING)
	{
		pager_udp_send_data(send_buf, payloadSize+PACKAGE_MIN_SIZE);
	}
}
/*********************************************************************
 * @fn      pkg_set_sub_volume
 *
 * @brief   主机设置或查询子音量
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_set_sub_volume(unsigned char *rxbuf,int payload_Length)
{
	int i = 0, cunt = 0;
	int temp_sub_volume = 0, temp_aux_volume=0;
	int pos=0;
	unsigned char *pData = rxbuf+PAYLOAD_START;
	printf("pkg_set_sub_volume...\n");
	int SetOrQuery=pData[pos++];
	switch (SetOrQuery)
	{
		case CMD_QUERY : // 查询
			respond_pkg_query_sub_volume(SetOrQuery,rxbuf);
			break;
		case CMD_SET : // 设置
			temp_sub_volume = g_sub_volume;
			temp_aux_volume = g_aux_volume;
			g_sub_volume = pData[pos++];
			if(pos<payload_Length)
			{
				//代表有本地音量字段
				g_aux_volume=pData[pos++];
			}
			respond_pkg_query_sub_volume(SetOrQuery,rxbuf);
			if(temp_sub_volume != g_sub_volume)
			{
				save_sysconf(INI_SECTION_BASIC,"Sub_Volume");//子音量保存
			}
			if(temp_aux_volume != g_aux_volume)
			{
				save_sysconf(INI_SECTION_BASIC,"Aux_Volume");//本地音量保存
			}

			break;
		default :
			break;
	}
}


/*********************************************************************
 * @fn      pkg_control_volume_add_min
 *
 * @brief   主机控制音量加/减
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_control_volume_add_min(unsigned char *rxbuf)
{
	int temp_volume = 0;

	int event_IsAdd=rxbuf[PAYLOAD_START];
	int step = rxbuf[PAYLOAD_START+1];
	if(!event_IsAdd)
	{
		step *=-1;
	}
	respond_null_payload_pkg(rxbuf);

	if(g_paging_status == PAGING_START)
	{
		if( step<0 && (pager_property.volume < abs(step)) )
		{
			pager_property.volume=0;
		}
		else{
			pager_property.volume += step;
		}
		if(pager_property.volume>100)
			pager_property.volume=100;
		pkg_query_current_status(NULL);
		printf("set g_paging_vol:%d\n", pager_property.volume);
	}
	else
	{
		temp_volume = g_system_volume;
		//如果是音量减，且当前系统音量已经比步进小了,那么音量为0
		if( step<0 && (g_system_volume < abs(step)) )
		{
			g_system_volume=0;
		}
		else{
			g_system_volume+=step;
		}
		if(g_system_volume>100)
			g_system_volume=100;
		pkg_query_current_status(NULL);
		g_pre_system_volume = g_system_volume;

		//如果是定时音源，那么记录此音量
		if( get_system_source() == SOURCE_TIMING)
		{
			g_timing_volume = g_system_volume;
		}
		
		if(temp_volume == g_system_volume)
		{
			return;
		}
		save_sysconf(INI_SECTION_BASIC,"System_Volume");//主机设置音量保存
	}
}



/*********************************************************************
 * @fn      Set_zone_idle_status
 *
 * @brief   设置分区空闲状态
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void Set_zone_idle_status(unsigned char *rxbuf, const char *function,int line, bool canRepeat_paly)
{       
	printf("Set_zone_idle_status:function=%s,line=%d\n", function, line);
      
	if(rxbuf!=NULL)
	{       
		respond_null_payload_pkg(rxbuf);
		if( g_paging_status == PAGING_START )
		{
		  	int IsValid=0;
			int Payload_Length = rxbuf[PAYLOAD_START-2]*256 + rxbuf[PAYLOAD_START-1]; // 负载数据长度
			if( Payload_Length>0 )
			{
				int authority=rxbuf[PAYLOAD_START];
				printf("[authority = %d]\n", authority);
				#if CANCEL_PAGER_SOURCE_PRIORITY
				IsValid=1;
				#else
				if(authority > pager_property.authority)
				{
					IsValid=1;
				}
				else if(authority == pager_property.authority)		//相同优先级的判断是否同一个MAC
				{
					unsigned char mac[6]={0};
					memcpy(mac,&rxbuf[PAYLOAD_START+1],6);
					if( memcmp(mac,pager_property.pager_mac,6) == 0 )
					{
						IsValid=1;
					}
				}
				if(IsValid == 0)
				{
					unsigned char client=rxbuf[4];
					int forwardPkg=rxbuf[5];
					if (client == DEVICE_MODEL_HOST && authority == 0x10 && !forwardPkg)
					{
						IsValid=1;
					}
				}
				#endif
			}
			if(!IsValid)
			{
			  	printf("Paging,Permission denied!\n");
				return;
			}
			
			paging_repeat_again_enable=0;
			pre_web_pager_strem_timeout=0;
			memset(pre_web_pager_cmd_pkg,0,sizeof(pre_web_pager_cmd_pkg));
			//g_paging_timing_count = PAGING_TIMING_COUNT_MAX;	//关闭寻呼检测线程

			printf("need exit paging...\n");
			return;
		}
		if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
		{
			printf("[g_media_source != AUX]\n");
			
			#if !LOCAL_SOURCE_PRIORITY_HIGHEST
			concentrate_repeat_paly_enable=0;
			mixed_source_repeat_again_enable=0;
			phone_gateway_source_repeat_again_enable=0;
			#endif
		}
		else
		{       
			//Dbg("[g_media_source == SOURCE_NULL]\n");
			return;
		}
	}
	else //自己设为空闲
	{
		if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
		{
		   concentrate_repeat_paly_enable=canRepeat_paly;
		}
	}

	g_centralized_mode_timing_repeat = 0;
	
	
	Clean_All_Audio();
	

	if(g_media_source != SOURCE_NULL && g_media_source != SOURCE_AUX)
	{   
		//音量恢复
		if(g_media_source == SOURCE_TIMING)
		{
			//g_system_volume=g_pre_system_volume;
		}
		else if(g_media_source == SOURCE_MONITOR_EV)
		{
			//g_system_volume=g_pre_system_volume;
		}
		else if(g_media_source == SOURCE_FIRE_ALARM)
		{
		   g_system_volume = g_pre_system_volume;	  	   //恢复音量
		}
		else if(g_media_source == SOURCE_NET_PAGING)
		{
			//g_system_volume = g_pre_system_volume;	  //恢复音量
		}
		else if(g_media_source == SOURCE_AUDIO_MIXED)
		{
			//g_audio_mixer_stream_timing_count = AUDIO_MIXER_TIMING_COUNT_MAX;
		}
		else if(g_media_source == SOURCE_PHONE_GATEWAY)
		{
			//g_phone_gateway_stream_timing_count = PHONE_GATEWAY_TIMING_COUNT_MAX;	//关闭寻呼检测线程
		}
	}

	g_system_volume = g_pre_system_volume;	  			//恢复音量
	g_timing_volume = -1;
	
	set_system_source(g_signal_100v ? SOURCE_100V_INPUT:SOURCE_NULL);

	g_media_status = SONG_STOP;	//设置为停止

	if(g_paging_status == PAGING_START)
	{
		g_paging_status = PAGING_STOP;
	}
	
	memset(g_media_name, 0x00, sizeof(g_media_name));
	pkg_query_current_status(NULL);
	
	printf("Set_zone_idle_status:ok\n");
}

/*********************************************************************
 * @fn      HOST_QUERY_SET_IP_INFO
 *
 * @brief   //主机向终端设置IP属性
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	int payload_len=(pkg_data[6]<<8)+pkg_data[7];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			printf("HOST_QUERY_SET_IP_INFO:MAC not match,return.\n");
			break;
		}
	}
	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("查询IP属性!\n");
			//IP分配方式
			memcpy(&data[0],g_mac_addr,6);
			data[6]=g_IP_Assign;
			if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				data_len=7;
			}
			else
			{
		        //静态IP地址
		        data_len=7;
		     	data[data_len++] = strlen(g_Static_ip_address);
		        memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
		     	data_len+=strlen(g_Static_ip_address);
		     	//子网掩码
		        data[data_len++] = strlen(g_Subnet_Mask);
		        memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
		        data_len+=strlen(g_Subnet_Mask);
		        //网关
		        data[data_len++] = strlen(g_GateWay);
		        memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
		        data_len+=strlen(g_GateWay);
		        //主DNS服务器
		        data[data_len++] = strlen(g_Primary_DNS);
		        memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
		        data_len+=strlen(g_Primary_DNS);
		        //备用DNS
		        data[data_len++] = strlen(g_Alternative_DNS);
		        memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
		        data_len+=strlen(g_Alternative_DNS);
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);

			if(g_network_mode == NETWORK_MODE_WAN)
			{
				#if ENABLE_TCP_CLIENT
				host_tcp_send_data(sendBuf, sendLen);
				#endif
			}
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set IP INFO!\n");
			pos++;
			int temp_len = 0;
			//IP分配方式
			int IP_Assign_temp = pkg_data[pos++];
			printf("IP_Assign_temp=%d\n",IP_Assign_temp);

			if(IP_Assign_temp == IP_ASSIGN_DHCP && g_IP_Assign == IP_ASSIGN_DHCP) //都为DHCP
			{
				printf("IP_Assign_temp=g_IP_Assign=IP_ASSIGN_DHCP,return!\n");
				break;
			}
			char TempStatic_ip_address[32]={0};
			char TempSubnet_Mask[32]={0};
			char TempGateWay[32]={0};
			char TempPrimary_DNS[32]={0};
			char TempAlternative_DNS[32]={0};

			if(IP_Assign_temp == IP_ASSIGN_STATIC)
			{
				//静态IP地址
				temp_len = pkg_data[pos++];
				memset(TempStatic_ip_address,0,sizeof(TempStatic_ip_address));
				memcpy(TempStatic_ip_address,&pkg_data[pos],temp_len);
				printf("g_Static_ip_address=%s,temp_len=%d\n",TempStatic_ip_address,temp_len);
				pos+=temp_len;
				//子网掩码
				temp_len=pkg_data[pos++];
				memset(TempSubnet_Mask,0,sizeof(TempSubnet_Mask));
				memcpy(TempSubnet_Mask,&pkg_data[pos],temp_len);
				printf("g_Subnet_Mask=%s,temp_len=%d\n",TempSubnet_Mask,temp_len);
				pos+=temp_len;
				//网关
				temp_len=pkg_data[pos++];
				memset(TempGateWay,0,sizeof(TempGateWay));
				memcpy(TempGateWay,&pkg_data[pos],temp_len);
				printf("g_GateWay=%s,temp_len=%d\n",TempGateWay,temp_len);
				pos+=temp_len;
				//主DNS服务器
				temp_len=pkg_data[pos++];
				memset(TempPrimary_DNS,0,sizeof(TempPrimary_DNS));
				memcpy(TempPrimary_DNS,&pkg_data[pos],temp_len);
				printf("g_Primary_DNS=%s,temp_len=%d\n",TempPrimary_DNS,temp_len);
				pos+=temp_len;
				//备用DNS
				temp_len=pkg_data[pos++];
				memset(TempAlternative_DNS,0,sizeof(TempAlternative_DNS));
				memcpy(TempAlternative_DNS,&pkg_data[pos],temp_len);
				printf("g_Alternative_DNS=%s,temp_len=%d\n",TempAlternative_DNS,temp_len);
				pos+=temp_len;

				//检测IP、掩码、网关是否输入错误
				if(!NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay) && \
					!isGatewayByNetmask_Error(TempStatic_ip_address,TempSubnet_Mask,TempGateWay))
				{
					printf("Net parameter check ok!\n");
				}
				else
				{
					//长度或内容错误,返回错误
					return;
				}

				if(strcmp(g_Static_ip_address, TempStatic_ip_address) == 0 && strcmp(g_Subnet_Mask, TempSubnet_Mask) == 0 && strcmp(g_GateWay, TempGateWay) == 0 &&\
					(g_IP_Assign == IP_Assign_temp) &&\
					strcmp(g_Primary_DNS, TempPrimary_DNS) == 0 && strcmp(g_Alternative_DNS, TempAlternative_DNS) == 0)
				{
					printf("HOST_QUERY_SET_IP_INFO:No parameter changed.%d\n", g_IP_Assign);
					return;
				}

				memcpy(g_Static_ip_address, TempStatic_ip_address, 32);
				memcpy(g_Subnet_Mask, TempSubnet_Mask, 32);
				memcpy(g_GateWay,TempGateWay,32);

				//DNS
				if(if_a_string_is_a_valid_ipv4_address(TempPrimary_DNS))
				{
					memcpy(g_Primary_DNS,TempPrimary_DNS,32);
				}
				else
				{
					memset(g_Primary_DNS,0,sizeof(g_Primary_DNS));
				}
				if(if_a_string_is_a_valid_ipv4_address(TempAlternative_DNS))
				{
					memcpy(g_Alternative_DNS,TempAlternative_DNS,32);
				}
				else
				{
					memset(g_Alternative_DNS,0,sizeof(g_Alternative_DNS));
				}
			}

			g_IP_Assign = IP_Assign_temp;

			Send_Unonline_Info();
			
			//设置成功，发送给主机
			memcpy(data, g_mac_addr, 6);
			data[6]=g_IP_Assign;
			if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				data_len=7;
			}
			else
			{
		        //静态IP地址
		        data_len=7;
		     	data[data_len++] = strlen(g_Static_ip_address);
		        memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
		     	data_len+=strlen(g_Static_ip_address);
		     	//子网掩码
		        data[data_len++] = strlen(g_Subnet_Mask);
		        memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
		        data_len+=strlen(g_Subnet_Mask);
		        //网关
		        data[data_len++] = strlen(g_GateWay);
		        memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
		        data_len+=strlen(g_GateWay);
		        //主DNS服务器
		        data[data_len++] = strlen(g_Primary_DNS);
		        memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
		        data_len+=strlen(g_Primary_DNS);
		        //备用DNS
		        data[data_len++] = strlen(g_Alternative_DNS);
		        memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
		        data_len+=strlen(g_Alternative_DNS);
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
			if(g_network_mode == NETWORK_MODE_WAN)
			{
				#if ENABLE_TCP_CLIENT
				host_tcp_send_data(sendBuf, sendLen);
				#endif
			}

			//保存网络信息
			save_sysconf(INI_SETCION_NETWORK,NULL);	
			System_Reboot();
		}
		break;
	}
}

//主机向终端设置网络模式(此条指令一般只允许主机以UDP发送，因为TCP模式下不允许随便设置模式，否则可能上不了线)
void ProcessHostSetNetWork(unsigned char * pkg,int pkg_len,bool isMulticast)
{
	switch (pkg[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("ProcessHostSetNetWork Query...\n");
			unsigned char sendBuf[MAX_BUF_SIZE]={0};
			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;

			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_NET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,CURRENT_DEVICE_MODEL,data_len,data);
			//20240928 增加组播查询
			multicast_send_data(sendBuf, sendLen);
		}
			break;
		case CMD_SET : 	//设置
		{
			int pos=PAYLOAD_START+1;
			int temp_network_mode=pkg[pos++];

			char temp_tcp_addr_domain[64]={0};	//主机地址(域名或者IP)
			int temp_host_tcp_port=0;			//主机TCP端口

			char temp_tcp_addr_domain2[64]={0};	//主机地址2(域名或者IP)
			int temp_host_tcp_port2=0;			//主机TCP端口2

			int ip_len=pkg[pos++];
			memset(temp_tcp_addr_domain,0,sizeof(temp_tcp_addr_domain));
			memcpy(temp_tcp_addr_domain,&pkg[pos],ip_len>64?64:ip_len);
			pos+=ip_len;
			temp_host_tcp_port=(pkg[pos]<<8)+pkg[pos+1];
			pos+=2;

			if(pos<pkg_len-1)
			{
				int ip_len=pkg[pos++];
				memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
				memcpy(temp_tcp_addr_domain2,&pkg[pos],ip_len>64?64:ip_len);
				pos+=ip_len;
				temp_host_tcp_port2=(pkg[pos]<<8)+pkg[pos+1];
				pos+=2;

				if(pos<pkg_len-1)
				{
					//存在mac时，判断mac是否和当前mac相同，如果不同，不处理
					if(	memcmp(pkg+pos,g_mac_addr,6)!= 0)
					{
					  	printf("ProcessHostSetNetWork:mac error!\n");
						return;
					}
					else
					{
						printf("ProcessHostSetNetWork:mac match!\n");
					}
				}
			}
			else
			{
				printf("not found tcp2...\n");
			}

			//判断temp_tcp_addr_domain和temp_tcp_addr_domain2是否是正确的IP地址或者域名
			if(temp_network_mode == NETWORK_MODE_WAN)
			{
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain) &&
				!is_valid_domain(temp_tcp_addr_domain) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain error\n");
					return;
				}
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain2) &&
				!is_valid_domain(temp_tcp_addr_domain2) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain2 error\n");
					memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
					temp_host_tcp_port2=0;
				}
			}

			if( g_network_mode!=temp_network_mode ||\
			    ( temp_network_mode == NETWORK_MODE_WAN &&\
				  ( (strcmp(temp_tcp_addr_domain,g_host_tcp_addr_domain)!=0 || temp_host_tcp_port != g_host_tcp_port ) ||\
				  	(strcmp(temp_tcp_addr_domain2,g_host_tcp_addr_domain2)!=0 || temp_host_tcp_port2 != g_host_tcp_port2 )\
				  )
				)
			  )
			{
				printf("Network Info CHANGE!\n");

				//todo 先设置空闲状态
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);

				//如果旧的是TCP模式,新的是UDP，那么需要关闭TCP线程
				if(g_network_mode == NETWORK_MODE_WAN && g_network_mode!=temp_network_mode)
				{
					g_network_mode=temp_network_mode;
					#if !NETWORK_VPN_INTERNET
					g_Is_tcp_real_internet=0;
					#endif
					tcp_client_exit();
				}
				//如果新的是TCP，两种情况：1、之前是UDP（开启TCP线程），2、TCP参数变化了（重连）
				else if(temp_network_mode == NETWORK_MODE_WAN)
				{
					memset(g_host_tcp_addr_domain,0,sizeof(g_host_tcp_addr_domain));
					memcpy(g_host_tcp_addr_domain, temp_tcp_addr_domain, sizeof(g_host_tcp_addr_domain));
					memset(g_host_tcp_addr_domain2,0,sizeof(g_host_tcp_addr_domain2));
					memcpy(g_host_tcp_addr_domain2, temp_tcp_addr_domain2, sizeof(g_host_tcp_addr_domain));
					printf("g_host_tcp_addr_domain:%s,g_host_tcp_addr_domain2:%s\n",g_host_tcp_addr_domain,g_host_tcp_addr_domain2);
					if(temp_host_tcp_port!=0)
					{
						g_host_tcp_port = temp_host_tcp_port;
						if(	g_host_tcp_port != DEFAULT_TCP_PORT )
						{
							g_host_kcp_port = g_host_tcp_port + 1;
						}
						else
						{
							g_host_kcp_port = DEFAULT_KCP_PORT;
						}

						g_host_tcp_port2 = temp_host_tcp_port2;
						if(	g_host_tcp_port2 != DEFAULT_TCP_PORT )
						{
							g_host_kcp_port2 = g_host_tcp_port2 + 1;
						}
						else
						{
							g_host_kcp_port2 = DEFAULT_KCP_PORT;
						}
					}
					printf("g_host_tcp_port:%d,g_host_tcp_port2:%d\n", g_host_tcp_port,g_host_tcp_port2);

					if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-15 )
					{
						g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-15;
						host_ready_offline_flag = 1;
						//关闭网络连接状态IO输出
						//GPIO_OutPut_Server_Connection(0);
					}

					//如果之前是UDP,新的是TCP，那么重启TCP线程
					if(g_network_mode == NETWORK_MODE_LAN)
					{
						g_network_mode=temp_network_mode;
						TCP_Client_Start();
					}
					else
					{
						tcp_client_reconnect();
					}
				}
				
				//保存网络信息
				save_sysconf(INI_SETCION_NETWORK,NULL);	
			}
			else
			{
				printf("Network Info NOT CHANGE!\n");
			}

			//发送查询结果
			unsigned char sendBuf[128]={0};

			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;


			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_NET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			host_udp_send_data(sendBuf, sendLen);
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,CURRENT_DEVICE_MODEL,data_len,data);
			//20240928 增加组播查询
			multicast_send_data(sendBuf, sendLen);
		}
			break;
		default :
			break;
	}
}


/*********************************************************************
 * @fn      Host_Set_SerialNumber
 *
 * @brief   //主机或配置工具向终端获取设备序列号（组播)
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_SerialNumber(unsigned char *pkg_data)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query SerialNumber!\r\n");
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			int sn_len=strlen(g_device_serialNum);
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			pos=PAYLOAD_START+7;
			int sn_len=pkg_data[pos++];
			if(sn_len>16)
			{
				break;
			}
			memset(g_device_serialNum,0,sizeof(g_device_serialNum));
			memcpy(g_device_serialNum,pkg_data+pos,sn_len);
			pos+=sn_len;
			
			printf("Set SerialNumber=%s\r\n",g_device_serialNum);
			//应答
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);

			save_sysconf(INI_SETCION_MFR,"SN");
		}
		break;
	}
}


/*********************************************************************
 * @fn      Host_Set_Dsp_Firmware_Feature
 *
 * @brief   主机或配置工具向终端查询/设置DSP固件功能特性（组播）
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data)
{
#ifndef USE_PC_SIMULATOR
	
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != g_mac_addr[i] )
		{
			mac_match=0;
			//Dbg("Host_Set_Dsp_Firmware_Feature:MAC not match,return.\r\n");
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query dsp feature!\n");
			memcpy(data,g_mac_addr,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set dsp feature!\n");
		  	pos=PAYLOAD_START+7;
		  	for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				dsp_firmware_feature.module_switch[i] = pkg_data[pos];
				dsp_firmware_feature.module_gain[i] = (pkg_data[pos+1]<<8) + (pkg_data[pos+2]);
#if 0
				if(dsp_firmware_feature.module_gain[i]>0x3FFF)
				  dsp_firmware_feature.module_gain[i]=0x3FFF;
				else if(dsp_firmware_feature.module_gain[i]<0x3FF)
				  dsp_firmware_feature.module_gain[i]=0x3FF;
#endif
				pos+=3;
			}
			//dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R] = 1;
		  
			for(i = 0;i< DSP_AUDIO_MODULE_MAX ;i++)
			{
				printf("Module[%d]:switch=%d,gain=%d\n",i,dsp_firmware_feature.module_switch[i],dsp_firmware_feature.module_gain[i]);
			}
	
		  	memcpy(data,g_mac_addr,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,CURRENT_DEVICE_MODEL,data_len,data);
			multicast_send_data(sendBuf, sendLen);
		  
			//保存配置信息
			save_sysconf(INI_SETCION_DSP_Firmware,NULL);
			
			System_Reboot();
		}
		break;
	}

#endif
}



/*********************************************************************
 * @fn      pkg_get_update_info
 *
 * @brief   获取主机推送的升级信息
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_get_update_info(unsigned char *rxbuf)
{
	int i;
	int data_len = 0;
	int payloadSize = 0;
	unsigned char data_buf[256];
	unsigned char verison[32]={0};

	char g_web_server_ip[48]={0};
	int g_web_server_port;
	char g_url_buf[64]={0};
	char g_url_real_buf[128]={0};


	unsigned char device_type=rxbuf[PAYLOAD_START+payloadSize];
	payloadSize++;
	if(device_type!=CURRENT_DEVICE_MODEL)
	{
		printf("\ndevice_type error,value=%d\n",device_type);
		return;
	}

	// 获取主机推送的固件版本
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;

	strcpy(verison,data_buf);

	// 获取web服务器的域名或IP
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;

	printf("Host web server ip or domain is : %s\n", data_buf);
	strncpy(g_web_server_ip,data_buf,sizeof(g_web_server_ip));

	// 获取主机web服务器端口
	g_web_server_port = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	payloadSize += 2;

	// 获取主机升级文件存放的绝对路径
	memset(g_url_buf, 0x00, sizeof(g_url_buf));
	if ((rxbuf[PAYLOAD_START+payloadSize]*256+rxbuf[PAYLOAD_START+payloadSize+1]) <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	}
	payloadSize += 2;
	for (i=0; i<data_len; i++)
	{
		g_url_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host update file path is : %s\n", g_url_buf);
#endif
	
	//g_update_type=UPDATE_TYPE_APP;
	#if 0
	//如果升级文件路径包括DISP，则判断DISP模块的版本号
	if(	strstr(g_url_buf,"DISP") )
	{
		//去掉前面的‘V’
		memset(firmaware_versionNoV,0,sizeof(firmaware_versionNoV));
		strncpy(firmaware_versionNoV,verison+1,strlen(verison)-1);
		if( strlen(g_DispVersion) == 0 && !g_CurDispUpgrading)
		{
			printf("The Disp Module is not Exist!\r\n");
			respond_pkg_update_status_code(UPDATE_FAIL);
			return;
		}
		else
		{
			if(strlen(g_DispVersion) >0 && strcasecmp(firmaware_versionNoV,g_DispVersion) <= 0)
			{
				printf("The Disp firmware is newest!\r\n");
				respond_pkg_update_status_code(FIRMWARE_NEWEST);
				return;
			}
		}
		g_update_type=UPDATE_TYPE_DISP;
	}
	else if(strcasecmp(verison, LOCAL_FIRMWARE_VERSION) <= 0)
	#else
	if(strcasecmp(verison, LOCAL_FIRMWARE_VERSION) <= 0)
	{
		printf("The local firmware is newest,not to update!\r\n");
		respond_pkg_update_status_code(FIRMWARE_NEWEST);
		return;
	}
	#endif

	
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		sprintf(g_web_server_ip,"%s",g_host_tcp_prase_ipAddress);
	}

	//组成g_url_real_buf
	sprintf(g_url_real_buf,"http://%s:%d%s",g_web_server_ip,g_web_server_port,g_url_buf);
	printf("g_url_buf=%s,g_url_real_buf=%s\n",g_url_buf,g_url_real_buf);

	//开始升级
	start_ota_task(g_url_real_buf);
}



/*********************************************************************
 * @fn      respond_pkg_update_status_code
 *
 * @brief   应答主机升级状态
 *
 * @param   code - 状态码
 *
 * @return	void
 */
int respond_pkg_update_status_code(unsigned char code)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=code;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_UPDATE_INFO,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
	return 0;
}



/*********************************************************************
 * @fn      send_download_rate_of_progress
 *
 * @brief   向主机发送下载进度
 *
 * @param   progress - 进度（%0~100%）
 *
 * @return	void
 */
int send_download_rate_of_progress(unsigned char progress)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=progress;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_RATE_PROGRESS,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	host_udp_send_data(sendBuf, sendLen);
	return 0;
}

/*********************************************************************
 * @fn      host_control_reboot
 *
 * @brief   主机控制终端重启
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
void host_control_reboot(char *rxbuf,unsigned char netType)
{
	respond_null_payload_pkg(rxbuf);

	//如果是组播，MAC不符返回
	if(netType == 1)
	{
		int zone_num = rxbuf[PAYLOAD_START];
		char mac_buf[MAX_BUF_SIZE] = {0};
		memcpy(mac_buf, &rxbuf[PAYLOAD_START+1], 6*zone_num);

		int i = 0,t = 0;
		int mac_ok = 0;
		for(i = 0; i < zone_num; i++)
		{
			if(memcmp(g_mac_addr,mac_buf+i*6,6) == 0)
			{
				mac_ok=1;
				break;
			}
		}
		if(!mac_ok)
		{
			return;
		}
	}
	
	printf("host_control_reboot...\n");
	System_Reboot();
}


/*********************************************************************
 * @fn      host_control_format
 *
 * @brief   主机向设备请求清除数据
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
void host_control_format(char *rxbuf)
{
	unsigned char sendBuf[MAX_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int data_len=1;

	int type = rxbuf[PAYLOAD_START];
	printf("host_control_format,type=%d\r\n",type);
	data[0] = type;
	int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_FORMAT,CURRENT_DEVICE_MODEL,data_len,data);
	// 发送数据
	unsigned char client = rxbuf[4];

	host_udp_send_data(sendBuf, sendLen);
	
	switch(type)
	{
		case 0xff://恢复出厂设置，清除除了DSP特性、SN外数据文件
			//将INI_SETCION_NETWORK、INI_SECTION_DEVICE、INI_SECTION_BASIC还原
			format_sysconf();
			System_Reboot();
			break;
		case 0x01:		//分区数据
			break;
		case 0x02:		//分组数据
			break;
		case 0x03:		//播放列表（包括已同步文件）
			break;
		case 0x04:		//定时数据
			break;
	}
}

/*********************************************************************
 * @fn      HOST_QUERY_SET_WORK_MODE
 *
 * @brief   主机向设备查询/设置工作模式
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_WORK_MODE(unsigned char *pkg_data)
{
	switch (pkg_data[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("QUERY work_mode=%d\n",g_system_work_mode);
			unsigned char sendBuf[MAX_BUF_SIZE] = {0};
			int i=0;
			unsigned char data[128] = {0};
			int data_len = 1;
			data[0] = g_system_work_mode;
			int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SET_WORK_MODE,CURRENT_DEVICE_MODEL,data_len,data);
			// 发送数据
			unsigned char client = pkg_data[4];
			if (client == DEVICE_MODEL_HOST)
			{
				// 发送数据
				host_udp_send_data(sendBuf, sendLen);
			}
			else if (client == DEVICE_MODEL_PAGING)
			{
				pager_udp_send_data(sendBuf, sendLen);
			}
		}
			break;
		case CMD_SET : 	//设置
		{
			respond_null_payload_pkg(pkg_data);
			int work_mode = pkg_data[PAYLOAD_START+1];
			printf("SET work_mode=%d\n",work_mode);
			if(g_system_work_mode == work_mode)
			{
				break;
			}
			g_system_work_mode = work_mode;
			if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)	//分布模式
			{
				printf("设置分布模式...\n");
				//停止所有播放歌曲
			}
			else	//集中模式
			{
				printf("设置集中模式...\n");
			}
			if( g_media_source != SOURCE_FIRE_ALARM && g_media_source != SOURCE_NET_PAGING )
			{
				printf("将设备设置为空闲状态\n");
				Set_zone_idle_status(NULL,  __func__, __LINE__,true);
			}
			send_online_info();
		}
		break;
		default :
			break;
	}
}

/*********************************************************************
 * @fn      network_pkg_process
 *
 * @brief   网络数据包处理
 *
 * @param   Pkg - 指向接收缓存
 *			Pkg_Length - 接收数据长度
 *
 * @return  none
 */
void network_pkg_process(unsigned char NetPkgType,unsigned char *Pkg, int Pkg_Length)
{
	int cmd_word = 0;
	int i = 0,dataLen=0;
	cmd_word = Pkg[0]*256 + Pkg[1];
	dataLen = Pkg[6]*256 + Pkg[7];

	int device_model = Pkg[4];
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		if( device_model != DEVICE_MODEL_HOST && device_model != DEVICE_MODEL_PAGING && device_model != DEVICE_MODEL_MOBILE
				&& device_model != DEVICE_MODEL_NETWORK_TOOLS)
		{
			//printf("process_host_pkg ERROR1:device_model=0x%x\n",device_model);
			return;
		}
	}
	else if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(device_model != DEVICE_MODEL_NETWORK_TOOLS && device_model != DEVICE_MODEL_HOST)
		{
			//printf("process_host_pkg ERROR2:device_model=0x%x\n",device_model);
			return;
		}
	}

	if(device_model == DEVICE_MODEL_HOST)
	{
		g_host_device_TimeOut=0;
	}

	//ESP_LOGI(TAG,"network_pkg_process:cmd_word=0x%04x,netType=%d",cmd_word,NetPkgType);

	switch(cmd_word)
	{
		#if ENABLE_TCP_CLIENT
		case CMD_TCP_CLIENT_CONNECTED:
		//TODO
			//kcp_send_heartbeat();
			if(g_network_mode == NETWORK_MODE_WAN)
			{
				if(power_on_flag==0)
				{
					power_on_flag = 1;
					power_on_notice_host();
				}
			}
		break;
		#endif
		case CMD_SET_DEVICE_ALIAS://修改设备别名
			Change_Device_Alias(Pkg);
		break;
		case CMD_TIME_SYN:	//时间同步
			pkg_set_local_time(Pkg);
		break;
		case CMD_SET_VOL://设置音量值
			pkg_set_volume(Pkg);
		break;
		case CMD_HOST_SET_SUB_VOLUME://设置子音量
			pkg_set_sub_volume(Pkg,dataLen);
		break;
		case CMD_HOST_SET_VOLUME_ADD_MIN://主机控制音量加/减
			pkg_control_volume_add_min(Pkg);
		break;
		case CMD_GET_ZONE_DETAIL_INFO://获取设备信息
			pkg_query_current_status(Pkg);
		break;
		case CMD_FIRMWARE_VERSION://查询当前版本信息
			//pkg_query_firmware_version(Pkg);
		break;
		case CMD_UPDATE_INFO://获取升级信息
			pkg_get_update_info(Pkg);
		break;
		case CMD_PLAY_NETWORK:// 主机点播歌曲
		break;

		case CMD_SET_ZONE_PLAYSTATUS://主机设置终端的播放状态
			//play_status_control(Pkg);
		break;
		case CMD_SET_ZONE_MUTE_STATUS://单播设置/查询终端静音状态
		break;
		case CMD_SET_ZONE_CONTROL_MODE://终端控制模式设置
		break;

		case CMD_QUERY_FILE://主机向终端查询存储在本地的文件信息
			//response_send_host_xml_file_info(Pkg);
		break;
		
		case CMD_GET_HOST_SYNC_INFO://主机向终端请求更新文件
			//Get_UpdateXML_Info_From_Host(Pkg);
		break;
		case CMD_SEND_HOST_MUSICLIST_SYNC_PROGRESS://向主机发送同步进度
			break;
		case CMD_GET_HOST_STOP_SYNC_MUSICLIST://主机停止同步歌曲
		break;
		case CMD_SET_RING://设置钟声
		break;
		case CMD_PLAY_RING://主机向终端请求播放钟声
		break;
		case CMD_PLAY_LOCAL_MUSIC://主机向终端请求播放本地歌曲
			//host_play_local_music(Pkg);
		break;
		case CMD_SET_PLAYMODE://主机设置终端的播放模式
			//play_mode_control(Pkg);
		break;
		case CMD_SET_ZONE_IDLE_STATUS://主机设置终端空闲模式
			Set_zone_idle_status(Pkg,  __func__, __LINE__,true);
		break;
		case CMD_SET_ZONE_MAC://主机请求重新分配MAC
			//Host_Set_Zone_MAC(Pkg);
		break;
		case CMD_CONTROL_REBOOT://主机向终端发送重启指令
			host_control_reboot(Pkg, 0);
		break;
		case CMD_CONTROL_FORMAT://主机向设备请求清除数据
			host_control_format(Pkg);
		break;
		case CMD_QUERY_FLASH_INFO://主机向设备请求查询FLASH信息
			//HOST_QUERY_FLASH_INFO(Pkg);
		break;
		case CMD_QUERY_MAC_INFO://主机向设备请求查询/设置MAC地址
			//HOST_QUERY_SET_MAC_INFO(Pkg);
		break;
		case CMD_QUERY_TIME_INFO://主机向设备请求查询设备日期时间
			//HOST_QUERY_TIME_INFO(Pkg);
		break;
		case CMD_QUERY_SET_WORK_MODE://工作模式
			HOST_QUERY_SET_WORK_MODE(Pkg);
		break;
		case CMD_CONCENTRATED_PLAY_SOURCE://集中模式下播放
			if(!(g_network_mode == NETWORK_MODE_WAN && NetPkgType == NET_TYPE_UNICAST_SERVER))
			{
				HOST_CONCENTRATED_PLAY_SOURCE(Pkg);
			}
		break;

		case CMD_HOST_AUDIO_STREAM://播放节目源数据流传输（TCP模式适用，UDP模式下直接发送数据到指定的组播地址中，无需命令字）
		case CMD_SEND_PCM_DATA_TCP:
		case CMD_WEB_PAGING_STREAM:
		case CMD_AUDIO_COLLECTOR_STREAM_TCP:
		case CMD_AUDIO_MIXER_STREAM:
		case CMD_PHONE_GATEWAY_STREAM:
			kcp_concentrated_ser_data(Pkg,Pkg_Length);
			break;

		case CMD_SET_ALARM_MUSIC_FILE://主机向终端设置警报声
			//host_set_Alarm_ring(Pkg);
		break;
		case CMD_SET_ALARM_STATUS://主机向终端设置开启/关闭警报
			//host_play_Alarm_ring(Pkg);
		break;
		case CMD_HOST_SET_AUDIO_COLLECOR_SOURCE://主机向音频采集器/终端设置音频采集音源
			HOST_SET_AUDIO_COLLECOR_SOURCE(Pkg);
		break;
		case CMD_HOST_QUERY_SET_NET_WORK_MODE://主机向终端设置网络模式
			ProcessHostSetNetWork(Pkg,Pkg_Length,false);
		break;
		case CMD_HOST_QUERY_SET_IP_INFO://主机向终端设置IP属性
			HOST_QUERY_SET_IP_INFO(Pkg);
		break;
		case CMD_HOST_QUERY_RECORD_LIST://主机获取设备记录文件列表
			//SendRecordListToHost(Pkg);
		break;
		case CMD_SEND_RECORD_FILE_CONTENT: //终端向主机发送记录文件内容
			//SendRecordFileContent(Pkg);
		break;
		case CMD_HOST_NET_DECODE_POWER_OUT_WAY://主机向网络解码播放器查询/设置电源输出模式
		break;
		case CMD_HOST_QUERY_AUDIO_RETURN_STATUS://主机向网络解码播放器查询回路检测状态
		break;
		case CMD_HOST_SET_EQ://主机向解码终端查询/设置高低音
			//host_set_eq(Pkg);
		break;
		case CMD_HOST_SET_EMMC_STATUS://主机向终端查询/设置EMMC工作状态
		break;
		case CMD_HOST_QUERY_SET_SOUND_MIXING://主机向终端查询/设置混音模式
			//ProcHostSetReqSoundMixing(Pkg);
		break;
		case CMD_HOST_QUERY_SET_PARTITION_DEVICE_STATUS://查询/设置网络解码分区器输出状态
		break;
        
        case CMD_HOST_SET_BLUETOOTH:                    //主机或配置工具向终端查询/设置蓝牙信息
            //Host_Set_BT_Info(Pkg);
        break;

		case CMD_SEND_PAGING_NOTIFY_TCP:				//通知终端开始寻呼(TCP模式) 服务器转发寻呼台
			//pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,0);
		break;

		case CMD_PAGING_AGAIN:
			if(paging_repeat_again_enable)
			{
				#if !CANCEL_PAGER_SOURCE_PRIORITY
				//pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,0);
				#endif
			}
		break;

		case CMD_HOST_NOTIFY_DECODER_READY_TIMING:		//主机通知解码终端即将进入定时音源
			//host_notify_decoder_ready_timing(Pkg);
		break;
	
		case CMD_WEB_PAGING_NOTIFY:			//服务器发起广播寻呼通知
		//memcpy(pre_web_pager_cmd_pkg,Pkg,sizeof(pre_web_pager_cmd_pkg));	//上一个APP寻呼的发起包,用于恢复寻呼
		//pkg_paging_notification(Pkg+PAYLOAD_START,dataLen,DEVICE_MODEL_PAGING,1);
		break;


		case CMD_HOST_SET_TRIGGER_CONFIG:
			//procHost_Query_Set_Trigger(Pkg,dataLen);;
		break;

		case CMD_SEND_TOOLS_DEVICE_EXTENSION:
			//procTools_Set_Extension(pkg_buf,pkg_len);
		break;

		case CMD_SEND_TOOLS_PRE_DEVICE_EXTENSION:
			//procTools_PreSet_Extension(pkg_buf,pkg_len);
		break;
	}
	
}





