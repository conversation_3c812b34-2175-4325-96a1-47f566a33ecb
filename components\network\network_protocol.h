/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 14:14:28 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2025-01-11 10:06:14
 */

#ifndef _NETWORK_PROTOCOL_H_
#define _NETWORK_PROTOCOL_H_

//该组播组用于接收终端发送的在线宣告（终端发送上线通知)
#define MCAST_CMD_SEND_PORT              52074
#define MCAST_CMD_SEND_ADDR              "**************"

#define MCAST_NETTOOLS_CMD_SEND_PORT     52075
#define MCAST_NETTOOLS_CMD_SEND_ADDR     "**************"

// 该组播组用于接收主机或寻呼器发送的指令
#define MCAST_CMD_RECV_PORT          52094
#define MCAST_CMD_RECV_ADDR          "**************"

// 该组播组用于接收寻呼器的PCM码流
#define MCAST_PAGING_RECV_PORT       52052
#define MCAST_PAGING_RECV_ADDR       "**************"

#define HOST_UDP_PORT 	     48888//主机单播数据接收发送端口号
#define NETTOOLS_UDP_PORT 	 48889//配置工具单播数据接收发送端口号（原来与HOST_UDP_PORT一样）
#define PAGER_UDP_PORT       46666//寻呼台单播数据接收发送端口号

#define DEFAULT_TCP_PORT       49888    //默认TCP端口
#define DEFAULT_KCP_PORT       49988    //默认KCP端口



#define MAX_BUF_NUM 	     10	    //网络数据接收缓冲包数量
#define MAX_BUF_SIZE 	     1450	//网络数据接收缓冲区大小
#define PACKAGE_MIN_SIZE	 9	    //网络包最小字节数
#define PAYLOAD_START        8      //负载字段开始位置
#define RESERVE              0x00   //保留字节

//静音定义
#define CMD_MUTE	0x10	//静音
#define CMD_NOMUTE	0x01	//非静音

#define CMD_SET  			 0x08   //设置
#define CMD_QUERY 			 0x80   //查询


/********工作模式定义***************/
#define WORK_MODE_DISTRIBUTIONAL 0x01 //分布模式 
#define WORK_MODE_CONCENTRATED	 0x02 //集中模式(默认)

/*******************寻呼状态*******************/
#define PAGING_STOP                  0x80
#define PAGING_START                 0x08

/******************寻呼类型*******************/
#define PAGING_TYPE_NULL 		0x00	//旧寻呼台，没有发送寻呼类型
#define PAGING_TYPE_MIC 		0x01	//MIC寻呼
#define PAGING_TYPE_APP 		0x02	//APP寻呼
#define PAGING_TYPE_MUSIC		0x03	//音乐寻呼

/******************混音器信号类型*******************/
#define MIXER_SIGNAL_TYPE_MIC   1   //信号类型：MIC
#define MIXER_SIGNAL_TYPE_AUX   2   //信号类型：AUX

/*******************设备音量*******************/
#define DEFAULT_SYSTEM_VOLUME               100
#define DEFAULT_SUB_VOLUME                  100
#define FIRE_ALARM_DEFAULT_VOL              100

/*播放状态*/
#define SONG_PLAYING          	0x01  // 播放
#define SONG_SUSPEND          	0x02  // 暂停
#define SONG_STOP             	0x04  // 停止

/*******************编码类型*******************/
#define DECODE_STANDARD_PCM          0x00       // 标准PCM
#define DECODE_G711         		 0x01  // G711
#define DECODE_G722         		 0x02  // G722
#define DECODE_G722_1         		 0x03  // G722.1

/*****************固件升级状态*/
#define START_DOWNLOAD        0x01                                /*开始下载*/
#define FIRMWARE_NEWEST       0x02                                /*本地固件已是最新版本*/
#define CONNECT_TIME_OUT      0x03                                /*连接服务器超时*/
#define UPDATE_FAIL           0x04                                /*升级失败*/
#define UPDATE_SUCCEED        0x05                                /*升级成功*/

/*******************API播放类型***************/
typedef enum {
    API_PLAY_NULL=0,
    API_PLAY_TTS=1,
    API_PLAY_URL=2
}API_PLAY_TYPE;

/*********************************
 * 程控/手控定义
 */
#define Terminal_AutoControl	0x01
#define Terminal_ManualControl	0x10

/*******************************
 * 时间
 */
#define TIME_SET_SUCCEED             0x08
#define TIME_FORMAT_ERROR            0x80


//设备型号定义
#define DEVICE_MODEL_HOST   			0x01	//主机
#define DEVICE_MODEL_NETWORK_TOOLS		0x02	//网络配置工具
#define	DEVICE_MODEL_PAGING 			0x03	//寻呼台
#define MODEL_IP_SPEAKER_A              0x05    //解码终端A（机架式）
#define MODEL_IP_SPEAKER_B              0x06    //解码终端B（户外防水、塑料箱）
#define MODEL_IP_SPEAKER_C              0x07    //解码终端C（木箱、壁挂）
#define MODEL_FIRE_COLLECTOR_A          0x08    //网络消防采集器A(旧)
#define MODEL_AUDIO_COLLECTOR_A         0x09    //网络音频采集器A(旧)
#define MODEL_IP_SPEAKER_D              0x0A    //解码终端D
#define MODEL_IP_SPEAKER_E              0x10    //解码终端E
#define MODEL_SEQUENCE_POWER_A          0x0B    //电源时序器A(旧)
#define MODEL_FIRE_COLLECTOR_B          0x0C    //网络消防采集器B
#define MODEL_AUDIO_COLLECTOR_B         0x0D    //网络音频采集器B
#define MODEL_SEQUENCE_POWER_B          0x0E    //电源时序器B
#define MODEL_REMOTE_CONTROLER          0x11    //远程遥控器
#define MODEL_AUDIO_MIXER_DECODER       0x12    //音频混音器-解码器
#define MODEL_AUDIO_MIXER_ENCODER       0x13    //音频混音器-编码器
#define MODEL_PHONE_GATEWAY             0x15    //电话网关
#define DEVICE_MODEL_MOBILE				0x20	//移动控制端

/*20250211 新增设备定义*/
#define MODEL_FIRE_COLLECTOR_C          0x16     // 消防采集器C
#define MODEL_AUDIO_COLLECTOR_C         0x17     // 音频采集器C
#define MODEL_SEQUENCE_POWER_C          0x18     // 电源时序器C
#define MODEL_REMOTE_CONTROLER_C        0x19     // 远程遥控器C
#define MODEL_AUDIO_MIXER_DECODER_C     0x1A     // 音频混音器C-解码器
#define MODEL_AUDIO_MIXER_ENCODER_C     0x1B     // 音频混音器C-编码器
/*********************/

/*20250222 新增设备定义*/
#define MODEL_IP_SPEAKER_F              0x1C      //解码终端F-易会系列
#define MODEL_FIRE_COLLECTOR_F          0x1D     // 消防采集器F-易会系列
#define MODEL_AUDIO_COLLECTOR_F         0x1E     // 音频采集器F-易会系列
#define MODEL_SEQUENCE_POWER_F          0x1F     // 电源时序器F-易会系列
#define MODEL_REMOTE_CONTROLER_F        0x30     // 远程遥控器F-易会系列

#define MODEL_IP_SPEAKER_G			    0x31     // 解码终端G
/*********************/

//节目源定义
#define SOURCE_NULL			 0x00
#define SOURCE_AUX			 0x01
#define SOURCE_LOCAL_PLAY	 0x02
#define SOURCE_TIMING		 0x03
#define SOURCE_AUDIO_MIXED	 0x04
#define SOURCE_CALL			 0x05
#define SOURCE_MONITOR_EV	 0X06 	//监控事件
#define SOURCE_FIRE_ALARM	 0x08
#define SOURCE_NET_PAGING	 0x09
#define SOURCE_100V_INPUT 	 0X0A

#define SOURCE_SIP_CALLING   0x0B   //SIP对讲
#define SOURCE_RTP_MULT      0x0C   //RTP_MULTI
#define SOURCE_API_TTS_MUSIC 0x0D   //API播放TTS或者音乐

#define SOURCE_TIMING_INTERCUT		 0x17//插播定时音源

#define SOURCE_PHONE_GATEWAY         0x20   //电话网关音源

#define SOURCE_AUDIO_COLLECTOR_BASE	 0x51	//0X51~0X78(40个)
#define SOURCE_AUDIO_COLLECTOR_MAX   0X78


#if LZY_COMMERCIAL_VERSION
//组播
#define CMD_SEARCH_DEVICE            0xFFAE //搜索在线终端设备
#define CMD_ONLINE                   0xFFAA //终端在线通知
#define CMD_OFFLINE                  0xFFEE //终端掉线通知
#else
//组播
#define CMD_SEARCH_DEVICE            0xFFFF //搜索在线终端设备
#define CMD_ONLINE                   0xFFFA //终端在线通知
#define CMD_OFFLINE                  0xFFFE //终端掉线通知
#endif

//单播
#define CMD_PAGING_NOTIFY    					0x0001      //寻呼通知
#define CMD_PAGING_STREAM_DATA    				0x0002      //寻呼音频流
#define CMD_TIME_SYN                            0x0003      //组播时间同步
#define CMD_SET_DEVICE_ALIAS					0x0004		//设置设备别名
#define CMD_SET_VOL								0x0005		//设置当前音量
#define CMD_GET_ZONE_DETAIL_INFO   				0x0006		//获取设备信息
#define CMD_FIRMWARE_VERSION					0x0007		//查询当前版本信息
#define CMD_UPDATE_INFO      					0x0008      //获取主机推送的固件升级信息
#define CMD_RATE_PROGRESS    					0x0009      //发送下载进度
#define CMD_PLAY_NETWORK     					0x000A      //主机点播歌曲（网络，暂未用到）
#define CMD_SET_ZONE_PLAYSTATUS            		0x000B	    //主机设置终端的播放状态
#define CMD_SET_ZONE_MUTE_STATUS				0x000C	    //单播设置/查询终端静音状态
#define CMD_SET_ZONE_CONTROL_MODE				0x000D	    //终端控制模式设置
#define CMD_QUERY_FILE               			0x000E	    //主机向终端查询存储在本地的文件信息
#define CMD_GET_HOST_SYNC_INFO					0x0010  	//主机向终端请求更新文件
#define CMD_SEND_HOST_MUSICLIST_SYNC_PROGRESS	0x0012      //向主机发送同步进度
#define CMD_GET_HOST_STOP_SYNC_MUSICLIST		0x0013      //主机停止同步歌曲
#define CMD_SET_RING         		 			0x0014	    //主机向终端请求设置钟声
#define CMD_PLAY_RING         		 			0x0015	    //主机向终端请求播放钟声
#define CMD_PLAY_LOCAL_MUSIC        			0x0016	    //主机向终端请求播放本地歌曲
#define CMD_SET_PLAYMODE               			0x0017	    //主机设置终端的播放模式
#define CMD_SET_ZONE_IDLE_STATUS	 			0x0018	    //主机设置终端空闲模式
#define CMD_SYN_HOST_TIME						0x0019	    //请求主机同步时间

/**********SIP相关**************/
#define CMD_QUERY_SIP_STATUS 	                0x001F	    //主机向设备查询SIP会话状态
#define CMD_CONTROL_SIP_LOGIN 	                0x0020	    //主机向设备请求SIP登录
#define CMD_QUERY_SIP_INFO 	 	                0x0021		//主机向设备请求查询SIP登录账号信息
/**********SIP相关**************/


#define CMD_SET_ZONE_MAC	 		 			0x001A	    //主机请求重新分配MAC
#define CMD_SEND_OFFLINE_PAGING_AGAIN	 		0x001B	    //寻呼台向终端发送再次寻呼指令
#define CMD_CONTROL_REBOOT		 				0x001C		//主机向终端发送重启指令
#define CMD_CONTROL_FORMAT 	 					0x001D		//主机向设备请求清除数据
#define CMD_QUERY_FLASH_INFO 					0x0022		//主机向设备请求查询FLASH信息
#define CMD_QUERY_MAC_INFO 	 	 				0x0023		//主机向设备请求查询/设置MAC地址
#define CMD_QUERY_TIME_INFO 	 				0x0024		//主机向设备请求查询设备日期时间
#define CMD_QUERY_SET_WORK_MODE 				0x0026		//查询/设置工作模式
#define CMD_CONCENTRATED_PLAY_SOURCE 			0x0028		//主机通知终端播放节目源（集中模式）
#define CMD_HOST_AUDIO_STREAM	 				0x0029		//播放节目源数据流传输（TCP模式适用，UDP模式下直接发送数据到指定的组播地址中，无需命令字）


#define CMD_SET_ALARM_MUSIC_FILE 				0x002B		//主机向终端设置警报声
#define CMD_SET_ALARM_STATUS 					0x002C		//主机向终端设置开启/关闭警报


#define CMD_HOST_SET_AUDIO_COLLECOR_SOURCE		0x0030	//主机向音频采集器/终端设置音频采集音源
#define CMD_HOST_QUERY_SET_NET_WORK_MODE 		0x0031 //主机向终端设置网络模式
#define CMD_HOST_QUERY_SET_IP_INFO 				0x0032  //主机向终端设置IP属性

//记录文件
#define CMD_HOST_QUERY_RECORD_LIST 				0x0033		//主机获取设备记录文件列表
#define CMD_SEND_RECORD_FILE_CONTENT			0x0034		//终端向主机发送记录文件内容
#define CMD_HOST_SEND_AUDIO_COLLECTOR_SELECTED	0x0036		//主机向采集器主动发送终端音源选择状态
#define CMD_SEND_AUDIO_COLLECTOR_STREAM  		0x0038		//采集器发送音频流

#define CMD_HOST_NET_DECODE_POWER_OUT_WAY		0x0039	//主机向网络解码播放器查询/设置电源输出模式
#define CMD_HOST_QUERY_AUDIO_RETURN_STATUS		0x003A	//主机向网络解码播放器查询回路检测状态

#define CMD_HOST_SET_EQ 						0x003C 	//主机向解码终端查询/设置高低音

#define CMD_HOST_SET_EMMC_STATUS 				0x003F //主机向终端查询/设置EMMC工作状态

#define CMD_SEND_PAGING_NOTIFY_MULTICAST		0x0040		//通知终端开始寻呼(组播)

#define CMD_HOST_QUERY_SET_SOUND_MIXING			0x0041		//主机向终端查询/设置混音模式

#define CMD_CONTROL_REBOOT_MULTICAST	 		0x0043		//主机向终端发送重启指令(组播)

#define CMD_SEND_PAGING_NOTIFY_TCP				0x0045		//通知终端开始寻呼(TCP模式)
#define CMD_SEND_PCM_DATA_TCP					0x0046		//发送PCM码流(TCP模式)
#define CMD_PAGING_AGAIN                        0X0047      //寻呼台向终端发送掉线再次寻呼指令

/***************消防采集器***************/
#define CMD_HOST_QUERY_FIRE_TRIG_STATUS			0x002E  //主机向消防采集器查询通道触发状态
#define CMD_HOST_SET_FIRE_TRIG_MODE				0x002F  //主机向消防采集器查询设置触发模式
#define CMD_SEND_FIRE_TRIG_STATUS				0x003D  //通道触发状态发生改变时主动通知主机
/*****************消防采集器***********/
   

#define CMD_HOST_QUERY_SET_PARTITION_DEVICE_STATUS 	0x004A  //查询/设置网络解码分区器输出状态


#define CMD_CLIENT_POWER_ON_NOTICE_HOST             0x0057  //终端仅在启动后接收到第一次的主机搜索在线终端设备(0xFFFF)时回复，用于记录终端开机状态

#define CMD_SEND_MULTICAST_CMD                      0x0059  //控制设备（包括主机）组播命令给终端


/**************************************/
#define CMD_HOST_SET_DSP_FIRMWARE_FEATURE	 		0x0060		//主机或配置工具向终端查询/设置DSP固件功能特性（组播）
#define CMD_HOST_SET_BLUETOOTH	 		            0x0061		//主机或配置工具向终端查询/设置蓝牙信息
#define CMD_TCP_CLIENT_CONNECTED                    0x0062		//TCP客户端连接后第一时间发送连接信息 

#define CMD_CALLING_INVITATION						0x0065		//对讲设备主叫方发起对讲邀请
#define CMD_CALLED_RESPONSE							0x0066		//对讲设备被叫方应答邀请
#define CMD_CALLED_STATUS							0x0067		//对讲设备状态反馈
#define CMD_CALLING_AUDIOSTREAM						0x0068		//对讲设备音频流传输

#define CMD_LISTEN_EVENT            				0x0071		//监听设备发起监听
#define CMD_LISTEN_RESPONSE                         0x0072      //被监听设备应答
#define CMD_LISTEN_STREAM_UPLOAD    				0x0073		//监听设备上传音频流
#define CMD_LISTEN_STATUS                           0X0074      //监听设备发送监听状态



#define CMD_WEB_PAGING_NOTIFY                       0x0077       //服务器发起广播寻呼通知
#define CMD_WEB_PAGING_STREAM                       0x0078       //服务器广播寻呼-音频流传输
#define CMD_AUDIO_COLLECTOR_STREAM_TCP              0x007A 		//音频采集器数据流发送（TCP)

#define CMD_HOST_SET_SERIAL_NUMBER                  0x007B 		//主机或配置工具向终端获取设备序列号（组播)

#define CMD_HOST_NOTIFY_DECODER_READY_TIMING        0x007C 		//主机通知解码终端即将进入定时音源

#define CMD_SEQUENCE_POWER_INFO                     0x0080 		//主机设置/查询时序器参数
#define CMD_SEQUENCE_POWER_TIMING                   0x0081 		//主机向电源时序器发送定时点信息

#define CMD_HOST_SET_AUDIO_MIXER_SOURCE             0x0082      //主机向混音器/终端设置混音音源
#define CMD_AUDIO_MIXER_STREAM                      0x0083 		//混音器音频流

#define CMD_HOST_SET_SUB_VOLUME                     0x0084      //主机查询/设置子音量

#define CMD_HOST_SET_VOLUME_ADD_MIN                 0x0085      //主机控制音量加/减

#define CMD_HOST_SET_REMOTE_CONTROLER_KEY           0x0086      //远程遥控器按键

#define CMD_HOST_SET_INTERCOM_BASIC_CONFIG          0x0087      //查询/设置对讲终端基础参数

#define CMD_HOST_SET_TRIGGER_CONFIG                 0x0088      //查询/设置触发参数
#define CMD_REQUEST_HOST_TRIGGER_PLAY               0x0089      //请求服务器播放触发歌曲

#define CMD_AUDIO_MIXER_CONFIG                      0x0096      //查询/设置音频混音器参数

#define CMD_AUDIO_COLLECTOR_CONFIG                  0x0097      //查询/设置音频采集器参数

#define CMD_PHONE_GATEWAY_CONFIG                    0x009C       //查询/设置电话网关参数
#define CMD_SET_PHONE_GATEWAY_SOURCE                0x009D       //主机向电话网关/终端设置电话网关音源
#define CMD_PHONE_GATEWAY_STREAM                    0x009E 	     //电话网关音频流
#define CMD_SEND_HOST_JSON_COMMAND                  0x009F 	     //终端向服务器发送json命令


#define CMD_SEND_TOOLS_DEVICE_EXTENSION             0x00A0 	     //终端向配置工具应答扩展功能
#define CMD_SEND_TOOLS_PRE_DEVICE_EXTENSION         0x00A1 	     //终端向配置工具应答预设置功能

#define CMD_STREAM_SOURCE_MULTICAST_NEW		        0x00B0		 //播放节目源数据流传输组播(新指令,加入命令字，以便后续离线存储)

#define CMD_INFORMATION_PUBLISH_CONFIG              0x00B1       //查询/设置信息发布参数

#define CMD_NETWORK_MODE_MULTICAST			        0x00B2		 //主机向终端设置网络模式(组播)


#endif
