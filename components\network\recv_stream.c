/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 16:22:30 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 17:12:32
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "freertos/event_groups.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "esp_log.h"
#include "esp_timer.h"

// TODO: 引用外部模块
#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "multicast.h"
#include "recv_stream.h"
#include "netPlay.h"

static const char *TAG = "RECV_STREAM";

stPager_property pager_property;
/***********Web Paging Start*******/
unsigned int pre_web_pager_strem_timeout;			//上一个APP寻呼流的超时时间
unsigned char pre_web_pager_cmd_pkg[MAX_BUF_SIZE];	//上一个APP寻呼的发起包1
/***********Web Paging End*******/

unsigned char mcast_song_recv_task_flag=0;          //组播歌曲/采集接收数据线程开启标志 1:已经启动 0：未启动
unsigned char mcast_song_recv_task_need_exit=0;	  //组播歌曲/采集接收数据线程需要退出标志

unsigned char mcast_pager_recv_task_flag=0;			//组播寻呼接收线程运行标志
unsigned char mcast_pager_recv_task_need_exit=0;		//组播寻呼接收线程需要退出标志

/*********************************************************************
 * @fn      mcast_recv_concentrated_data
 *
 * @brief   集中模式下数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
static void mcast_recv_concentrated_data(void *pvParameters)
{
	ESP_LOGI(TAG, "enter mcast_recv_concentrated_data");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	socklen_t cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	int64_t before_time = 0;
	int64_t current_time = 0;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int recv_type=0;

	int concentrated_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
	if(g_concentrated_start)
	{	
		recv_type=1;
		server_addr.sin_port = htons(g_concentrated_multicast_port);
		mreq.imr_multiaddr.s_addr = inet_addr(g_concentrated_multicast_address); // 注册组播地址
	}
	else if(g_collector_run_flag)
	{
		recv_type=2;
		server_addr.sin_port = htons(g_ac_mcast_port);
		mreq.imr_multiaddr.s_addr = inet_addr(g_ac_multicast_address); // 注册组播地址
	}	
	else
	{
		ESP_LOGE(TAG, "mcast_recv_concentrated_data:enter error!");
		vTaskDelete(NULL);
	}
	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (concentrated_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		ESP_LOGE(TAG, "Create mcast_recv_paging_data socket error");
		vTaskDelete(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(concentrated_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		ESP_LOGE(TAG, "setsockopt:mcast_recv_paging_data concentrated_sockfd SO_REUSEADDR failed");
		close(concentrated_sockfd);
		vTaskDelete(NULL);
	}

	// 绑定地址到套接字
	if ( bind(concentrated_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		ESP_LOGE(TAG, "bind:concentrated_sockfd");
		close(concentrated_sockfd);
		vTaskDelete(NULL);
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(concentrated_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		ESP_LOGE(TAG, "setsockopt:concentrated_sockfd");
		close(concentrated_sockfd);
		vTaskDelete(NULL);
	}


	int exit_cnt = 0;
	unsigned char buf[MAX_BUF_SIZE]={0};
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(concentrated_sockfd, &readfd); // 注册套接字文件描述符

		if(mcast_song_recv_task_need_exit)
		{
			break;
		}
		if(recv_type == 1)			//歌曲播放
		{
			if(!g_concentrated_start || server_addr.sin_port!=htons(g_concentrated_multicast_port))
			{
				exit_cnt++;
				if(exit_cnt>=1)
				{
					break;
				}
			}
		}
		else if(recv_type == 2)		//采集音源
		{
			if(!g_collector_run_flag || server_addr.sin_port!=htons(g_ac_mcast_port))
			{
				exit_cnt++;
				if(exit_cnt>=1)
				{
					break;
				}
			}
		}

		ret = select(concentrated_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_recv_paging_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				if (FD_ISSET(concentrated_sockfd, &readfd))
				{
					if(recv_type == 1 && g_IsCentralized_mode_multicast_new_cmd)
					{
						int pkg_length=recvfrom(concentrated_sockfd, buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

						int cmd_word = buf[0]*256 + buf[1];
						int payload_length=0;
						if(pkg_length<9 || cmd_word == 0)
							break;
						payload_length = buf[PAYLOAD_START-2]*256+buf[PAYLOAD_START-1];
						if(payload_length > MAX_BUF_SIZE || payload_length == 0)
						{
							break;
						}
						if(Calculate_XorDat(&buf[PAYLOAD_START], payload_length) != buf[pkg_length-1])
						{
							break;
						}

						unsigned char *payloadBuf=buf+PAYLOAD_START;
						if(cmd_word != CMD_STREAM_SOURCE_MULTICAST_NEW)
						{
							break;
						}
						// 集中模式音频（新的格式，有协议头）
						//payloadBuf,payload_length
						setStreamRecvBuf(payloadBuf,payload_length);
					}
					else	//旧的格式(只有音频流没有协议头,包括旧服务器和所有的音频采集音频)
					{
						//concentrated_pcm_buf_len[concentrated_write_pos] = recvfrom(concentrated_sockfd, &concentrated_pcm_buf[concentrated_write_pos], MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);
						if(recv_type == 2)	//旧的只有采集
						{
							int pkg_length=recvfrom(concentrated_sockfd, buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);
							setStreamRecvBuf(buf,pkg_length);
						}
					}
					
				}
			    break;
		}
	}
	close(concentrated_sockfd);

	printf("exit mcast_recv_concentrated_data!\n");
	mcast_song_recv_task_flag=0;
	
	vTaskDelete(NULL);
}

/*********************************************************************
 * @fn      start_concentrated_recv_pthread
 *
 * @brief   启动集中模式下数据接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_concentrated_recv_pthread(void)
{
	int count=30;
	while(	count-- && mcast_song_recv_task_flag )
	{
		mcast_song_recv_task_need_exit=1;
		vTaskDelay(pdMS_TO_TICKS(10));
	}
	mcast_song_recv_task_flag=1;
	mcast_song_recv_task_need_exit=0;

	printf("start_concentrated_recv_pthread:count=%d\n",count);

	xTaskCreate(mcast_recv_concentrated_data, "mcast_recv_con", 4096, NULL, 5, NULL);
}



#if 0
/*********************************************************************
 * @fn      mcast_recv_paging_data
 *
 * @brief   寻呼数据（组播）接收线程
 *
 * @param   void
 *
 * @return	void
 */
void mcast_recv_paging_data(void *arg)
{
	mcast_pager_recv_task_flag=true;
	printf("enter mcast_recv_paging_data\n");
	int rxlen = 0;
	int ret = -1;
	struct sockaddr_in server_addr, client_addr;
	struct ip_mreq mreq;
	fd_set readfd;
	struct timeval timeout;
	int cli_len = sizeof(client_addr);
	int i;
	int payload_length = 0;
	int pkg_length = 0;

	int rxIndex = 0;
	float before_time = 0;
	float current_time = 0;
	struct timeval time;
	unsigned int pcm_capacity = 0;
	unsigned char pcm_buf_start_read_flag = 0;

	int paging_sockfd;

	memset(&server_addr, 0x00, sizeof(server_addr));
	server_addr.sin_family = AF_INET;
	server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
	
	server_addr.sin_port = htons(MCAST_PAGING_RECV_PORT);
	mreq.imr_multiaddr.s_addr = inet_addr(MCAST_PAGING_RECV_ADDR); // 注册组播地址
	

	mreq.imr_interface.s_addr = htonl(INADDR_ANY);                 // 允许任何地址访问

	// 创建套接字
	if ( (paging_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		perror("Create mcast_recv_paging_data socket error");
		mcast_pager_recv_task_flag=false;
		vTaskDelete(NULL);
	}

	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(paging_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		perror("setsockopt:mcast_recv_paging_data paging_sockfd SO_REUSEADDR failed");
		close(paging_sockfd);
		mcast_pager_recv_task_flag=false;
		vTaskDelete(NULL);
	}

	// 绑定地址到套接字
	if ( bind(paging_sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) <0)
	{
		perror("bind:paging_sockfd");
		close(paging_sockfd);
		mcast_pager_recv_task_flag=false;
		vTaskDelete(NULL);
	}

	// 加入组中，才能接收组播源发出的数据
	if (setsockopt(paging_sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) < 0)
	{
		perror("setsockopt:paging_sockfd");
		close(paging_sockfd);
		mcast_pager_recv_task_flag=false;
		vTaskDelete(NULL);
	}


	int exit_cnt = 0;
	unsigned char paging_multi_buf[MAX_BUF_SIZE]={0};
	while (1)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 50000;
		FD_ZERO(&readfd);        // 清空读文件描述集合
		FD_SET(paging_sockfd, &readfd); // 注册套接字文件描述符


		if(g_paging_status == PAGING_STOP || mcast_pager_recv_task_need_exit)
		{
			printf("mcast_recv_paging_data need exit!\n");
			break;
		}
		ret = select(paging_sockfd+1, &readfd, NULL, NULL, &timeout);
		switch (ret)
		{
			case -1 :
				//memset(rxbuf_macast_pcm, 0x00, sizeof(rxbuf_macast_pcm)); // 清空接收缓存
				perror("mcast_recv_paging_data call select error");
				break;

			case 0 :	//超时
				//perror("mcast_recv_paging_data call select TimeOut");
				break;

			default : // 接收数据
				memset(paging_multi_buf,0,sizeof(paging_multi_buf));
				recvfrom(paging_sockfd, paging_multi_buf, MAX_BUF_SIZE, 0, (struct sockaddr*)&client_addr, &cli_len);

				payload_length = paging_multi_buf[PAYLOAD_START-2]*256+paging_multi_buf[PAYLOAD_START-1];
				pkg_length = payload_length+PACKAGE_MIN_SIZE;
				if(pkg_length > MAX_BUF_SIZE || payload_length == 0)
				{
					break;
				}
				if(Calculate_XorDat(&paging_multi_buf[PAYLOAD_START], payload_length) == paging_multi_buf[pkg_length-1])
				{
				   //判断发送数据的寻呼台MAC是否一致
					int mac_match=1;
					unsigned char pager_mac[6]={0};
					memcpy(pager_mac,paging_multi_buf+PAYLOAD_START,6);
					for(i=0;i<6;i++)
					{
						if(pager_mac[i] != pager_property.pager_mac[i])
						{
							mac_match=0;
							break;
						}
					}
					if(!mac_match)
					{
						printf("[Paging_Multi_Server_Data]mac not match\n");
						continue;
					}
					
					#if 0
					if(payload_length-6 >PAGER_PCM_BASIC_VALUE)
					{
						continue;
					}
					if(g_paging_timing_count<PAGING_TIMING_COUNT_MAX)
						g_paging_timing_count = 0;
					

					//判断寻呼类型（从保留字可以得出，旧寻呼台不支持，判断依据为发起寻呼通知指令是否带上pagingTYpe类型，且为1或者3）
					if(pager_property.pagingType == PAGING_TYPE_MIC || pager_property.pagingType == PAGING_TYPE_MUSIC)
					{
						int new_pagingType= paging_multi_buf[3];
						if( new_pagingType!= pager_property.pagingType && (new_pagingType == PAGING_TYPE_MIC || new_pagingType == PAGING_TYPE_MUSIC) )
						{
							pager_property.pagingType = new_pagingType;
							//Clean_Pager_Pcm_Buf();
							printf("new PagingType=%d\r\n",new_pagingType);
						}
					}

					for(i = 0; i < payload_length-6; i++)
					{
						if(pager_pcm_write>=sizeof(pager_pcm_buf[0]))
						{
							printf("Error:pager_pcm_write>=sizeof(pcm_buf[0]\r\n");
							break;
						}
						pager_pcm_buf[pager_write_pos][pager_pcm_write++] = paging_multi_buf[PAYLOAD_START+6+i];
					}
					pager_pcm_capacity++;
		
					if(pager_property.decodeType == DECODE_STANDARD_PCM && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
					{
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;	
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#if SUPPORT_CODEC_G722
					else if(pager_property.decodeType == DECODE_G722 && pager_pcm_capacity >= PAGER_PCM_READ_VALVE)
					{
						if(pager_pcm_write>512)
						{
							printf("Error:pager_pcm_write g722>512\r\n");
							pager_pcm_capacity = 0;
							pager_pcm_write = 0;
							continue;
						}
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;	
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#endif
					#if SUPPORT_CODEC_G7221
					else if(pager_property.decodeType == DECODE_G722_1 && pager_pcm_capacity >= 1)
					{
						pager_pcm_buf_len[pager_write_pos] = pager_pcm_write;
						pager_pcm_buf_valid[pager_write_pos] = 1;
						pager_write_pos++;
						if(pager_write_pos >= PAGER_PCM_BUF_MAX)
							pager_write_pos = 0;
						pager_write_pos_total++;
						pager_pcm_capacity = 0;
						pager_pcm_write = 0;
					}
					#endif

					#endif
				}
			    break;
		}
	}
	close(paging_sockfd);

	printf("exit mcast_recv_paging_data!\n");
	mcast_pager_recv_task_flag=0;	//组播寻呼接收线程运行标志
	vTaskDelete(NULL);
}


/*********************************************************************
 * @fn      start_mcast_paging_recv_pthread
 *
 * @brief   启动寻呼数据（组播）接收线程
 *
 * @param   void
 *
 * @return	void
 */
void start_mcast_paging_recv_pthread(void)
{
	int count=30;
	while(	count-- && mcast_pager_recv_task_flag )
	{
		mcast_pager_recv_task_need_exit=1;
		vTaskDelay(pdMS_TO_TICKS(10));
	}
	mcast_pager_recv_task_flag=1;
	mcast_pager_recv_task_need_exit=0;

	printf("start_mcast_paging_recv_pthread:count=%d\n",count);

	xTaskCreate(mcast_recv_paging_data, "mcast_recv_paging", 4096, NULL, 10, NULL);
}
#endif


void kcp_concentrated_ser_data(unsigned char *buf,int len)
{
	int i=0;
    int cmd_word = buf[0]*256 + buf[1];
	//printf("kcp_concentrated_ser_data:cmd_word=0x%x\n",cmd_word);
	int payload_length=0;

	if(len<9 || cmd_word == 0)
		return;
	payload_length = buf[PAYLOAD_START-2]*256+buf[PAYLOAD_START-1];
	if(payload_length > MAX_BUF_SIZE || payload_length == 0)
	{
		return;
	}
	if(Calculate_XorDat(&buf[PAYLOAD_START], payload_length) != buf[len-1])
	{
		return;
	}

	unsigned char *payloadBuf=buf+PAYLOAD_START;

	if(	get_system_source() == SOURCE_LOCAL_PLAY || get_system_source() == SOURCE_TIMING || get_system_source() == SOURCE_FIRE_ALARM ||
		(get_system_source() >= SOURCE_AUDIO_COLLECTOR_BASE && get_system_source() <= SOURCE_AUDIO_COLLECTOR_MAX) )
	{
		if(cmd_word != CMD_HOST_AUDIO_STREAM && cmd_word != CMD_AUDIO_COLLECTOR_STREAM_TCP)
		{
			printf("cmd_word:0x%04x != CMD_HOST_AUDIO_STREAM or CMD_AUDIO_COLLECTOR_STREAM_TCP...\n",cmd_word);
			return;
		}

		if(cmd_word == CMD_HOST_AUDIO_STREAM && !(get_system_source() == SOURCE_LOCAL_PLAY || get_system_source() == SOURCE_TIMING || get_system_source() == SOURCE_FIRE_ALARM))
		{
			return;
		}
		else if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP && !((get_system_source() >= SOURCE_AUDIO_COLLECTOR_BASE && get_system_source() <= SOURCE_AUDIO_COLLECTOR_MAX)) )
		{
			return;
		}

		if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP && !g_collector_run_flag)
			return;

		if(cmd_word == CMD_AUDIO_COLLECTOR_STREAM_TCP)
		{
			setStreamRecvBuf(payloadBuf+2,payload_length-2);
		}
		else
		{
			setStreamRecvBuf(payloadBuf,payload_length);
		}
	}
	#if 0
	else if(cmd_word == CMD_SEND_PCM_DATA_TCP || cmd_word == CMD_WEB_PAGING_STREAM)
	{
		if( cmd_word == CMD_WEB_PAGING_STREAM )
		{
			if( g_paging_status != PAGING_START )
			{
				//计时，超过3秒重新如果还在发，代表非手动停止，那么重新开始寻呼
				pre_web_pager_strem_timeout++;
				if(pre_web_pager_strem_timeout >= 50)
				{
					int pre_cmd_word = pre_web_pager_cmd_pkg[0]*256 + pre_web_pager_cmd_pkg[1];
					int pre_dataLen = pre_web_pager_cmd_pkg[6]*256 + pre_web_pager_cmd_pkg[7];
					if(pre_cmd_word == CMD_WEB_PAGING_NOTIFY)
					{
						//开始
						printf("ready resume web pager!\n");
						pkg_paging_notification(pre_web_pager_cmd_pkg+PAYLOAD_START,pre_dataLen,DEVICE_MODEL_PAGING,1);
					}
				}
			}
			else
			{
				if(pre_web_pager_strem_timeout >= 50)
				{
					pre_web_pager_strem_timeout++;
					if(pre_web_pager_strem_timeout>=100)
					{
						pre_web_pager_strem_timeout=0;
						printf("ready resume recv!\n");
					}
					else
					{
						return;
					}
				}
				else
				{
					pre_web_pager_strem_timeout = 0;
				}
			}
		}
		if(get_system_source() == SOURCE_NET_PAGING)
		{
			//判断寻呼类型（从保留字可以得出，旧寻呼台不支持，判断依据为发起寻呼通知指令是否带上pagingTYpe类型，且为1或者3）
			if(cmd_word == CMD_SEND_PCM_DATA_TCP)
			{
				if(pager_property.pagingType == PAGING_TYPE_MIC || pager_property.pagingType == PAGING_TYPE_MUSIC)
				{
					int new_pagingType= buf[3];
					if( new_pagingType!= pager_property.pagingType && (new_pagingType == PAGING_TYPE_MIC || new_pagingType == PAGING_TYPE_MUSIC) )
					{
						pager_property.pagingType = new_pagingType;

						Clean_Pager_Pcm_Buf();
						printf("new PagingType=%d\r\n",new_pagingType);
					}
				}
			}
			Kcp_Pager_Stream_Recv(payloadBuf,payload_length);
		}
	}
	//混音器音频流
	else if(cmd_word == CMD_AUDIO_MIXER_STREAM)
	{
		//printf("KCP:CMD_AUDIO_MIXER_STREAM...\n");
		if(get_system_source() != SOURCE_AUDIO_MIXED)
		{
			return;
		}
		//判断发送数据的MAC是否一致
		int mac_match=1;
		unsigned char mixer_mac[6]={0};
		memcpy(mixer_mac,buf+PAYLOAD_START,6);
		for(i=0;i<6;i++)
		{
			if(mixer_mac[i] != g_audio_mixer_mac[i])
			{
				mac_match=0;
				break;
			}
		}
		if(!mac_match)
		{
			return;
		}

		//保存音频编码
		g_audio_mixer_codecs = buf[PAYLOAD_START+6];

		//判断混音信号类型
		int new_signalType= buf[PAYLOAD_START+7];
		if(new_signalType!= g_audio_mixer_signalType)
		{
			//如果原来不是MIC信号，现在有MIC信号了，那么需要清除缓冲区，确保最低时延
			if(new_signalType == MIXER_SIGNAL_TYPE_MIC)
			{
				Clean_Mixed_Info();
			}
			g_audio_mixer_signalType = new_signalType;
			printf("g_audio_mixer_signalType=%d\n",new_signalType);
		}

		audioMixer_stream_len[audioMixer_stream_write_pos] = payload_length-8;
		memcpy(audioMixer_stream_buf[audioMixer_stream_write_pos],buf+PAYLOAD_START+8,audioMixer_stream_len[audioMixer_stream_write_pos]);
		g_audio_mixer_stream_timing_count = 0;
		//printf("write_pos=%d,len=%d\n",audioMixer_stream_write_pos,concentrated_pcm_buf_len[concentrated_write_pos]);
		audioMixer_stream_valid[audioMixer_stream_write_pos] = 1;
		audioMixer_stream_write_pos++;
		audioMixer_stream_write_pos_total++;
		if(audioMixer_stream_write_pos >= AUDIO_MIXER_STREAM_BUF_MAX)
		{
			audioMixer_stream_write_pos = 0;
		}
	}
	//电话网关音频流
	else if(cmd_word == CMD_PHONE_GATEWAY_STREAM)
	{
		//printf("KCP:CMD_PHONE_GATEWAY_STREAM...\n");
		if(get_system_source() != SOURCE_PHONE_GATEWAY)
		{
			return;
		}
		//判断发送数据的MAC是否一致
		int mac_match=1;
		unsigned char phone_mac[6]={0};
		memcpy(phone_mac,buf+PAYLOAD_START,6);
		for(i=0;i<6;i++)
		{
			if(phone_mac[i] != g_phone_gateway_mac[i])
			{
				mac_match=0;
				break;
			}
		}
		if(!mac_match)
		{
			return;
		}

		//保存音频编码
		g_phone_gateway_codecs = buf[PAYLOAD_START+6];

		phoneGateway_stream_len[phoneGateway_stream_write_pos] = payload_length-8;
		memcpy(phoneGateway_stream_buf[phoneGateway_stream_write_pos],buf+PAYLOAD_START+8,phoneGateway_stream_len[phoneGateway_stream_write_pos]);
		g_phone_gateway_stream_timing_count = 0;
		//printf("write_pos=%d,len=%d\n",phoneGateway_stream_write_pos,phoneGateway_stream_len[phoneGateway_stream_write_pos]);
		phoneGateway_stream_valid[phoneGateway_stream_write_pos] = 1;
		phoneGateway_stream_write_pos++;
		phoneGateway_stream_write_pos_total++;
		if(phoneGateway_stream_write_pos >= PHONE_GATEWAY_STREAM_BUF_MAX)
		{
			phoneGateway_stream_write_pos = 0;
		}
	}
	#endif
}