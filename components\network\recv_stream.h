/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 16:22:43 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 16:30:14
 */

#ifndef _PAGING_STREAM_H_
#define _PAGING_STREAM_H_

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "const.h"

#define CENTRALIZED_MODE_TIMING_COUNT_MAX 	25//(5s)
#define AUDIO_COLLECT_TIMING_COUNT_MAX      6//(6s)
#define PAGING_TIMING_COUNT_MAX             50//(5s)

#define AUDIO_CALL_TIMING_COUNT_MAX         500//(5s)
#define AUDIO_MIXER_TIMING_COUNT_MAX        50//(5s)
#define PHONE_GATEWAY_TIMING_COUNT_MAX      50//(5s)

typedef struct
{
    unsigned char authority;
    unsigned char volume;
    unsigned char pager_mac[6];
    int audioRate;
    unsigned char audioFMT;
    unsigned char audioChannels;
    unsigned char decodeType;
    unsigned char multicastIP[16];
	int multicastPort;
	unsigned char pagingType;	//呼叫类型，0-旧寻呼台 1-寻呼台MIC寻呼	2-WEB广播寻呼	3-寻呼台音乐广播
}stPager_property;

extern stPager_property pager_property;

/***********Web Paging Start*******/
extern unsigned int pre_web_pager_strem_timeout;			//上一个APP寻呼流的超时时间
extern unsigned char pre_web_pager_cmd_pkg[MAX_BUF_SIZE];	//上一个APP寻呼的发起包1
/***********Web Paging End*******/

// 将 pthread_mutex_t 替换为 FreeRTOS 的 SemaphoreHandle_t
extern SemaphoreHandle_t mutex_cleanPagerStream;	//清理寻呼音频流互斥锁
extern SemaphoreHandle_t mutex_cleanMixedStream;	//清理混音音频流互斥锁
extern SemaphoreHandle_t mutex_cleanPhoneGatewayStream;	//清理电话网关音频流互斥锁

extern unsigned char mcast_song_recv_task_flag;
extern unsigned char mcast_song_recv_task_need_exit;
extern unsigned char mcast_pager_recv_task_flag;
extern unsigned char mcast_pager_recv_task_need_exit;

void start_concentrated_recv_pthread(void);
void start_mcast_paging_recv_pthread(void);
void kcp_concentrated_ser_data(unsigned char *buf,int len);
#endif