/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-06 13:54:01 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-07 12:55:17
 */

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "lwip/sockets.h"
#include "lwip/netdb.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "esp_log.h"
#include "esp_timer.h"

#include "sysconf.h"
#include "network_protocol.h"
#include "network_process.h"
#include "udp_client.h"
#include "tcp_client.h"
#include "multicast.h"

static const char *TAG = "UDP_CLIENT";

struct sockaddr_in udp_host_addr,udp_pager_addr,udp_networktools_addr;
struct sockaddr_in tmp_udp_host_addr,tmp_udp_pager_addr,tmp_udp_networktools_addr;

static int udp_host_send_socket=-1,udp_pager_send_socket=-1,udp_networkTools_send_socket=-1,udp_networkTools_send_socket_new=-1;
static struct sockaddr_in udp_host_send_sockAddr,udp_pager_send_sockAddr,udp_networkTools_send_sockAddr,udp_networkTools_send_sockAddr_new;

/*********************************************************************
 * @fn      host_udp_send_data
 *
 * @brief  	发送应答主机数据
 *
 * @param   data--数据缓冲区
 * 			len--数据长度
 *
 * @return  none
*********************************************************************/
void host_udp_send_data(unsigned char * data,unsigned short len)  
{   
   if(udp_host_send_socket == -1)
   {
       udp_host_send_socket = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&udp_host_send_sockAddr, 0, sizeof(udp_host_send_sockAddr));
       udp_host_send_sockAddr.sin_family = AF_INET;
	   udp_host_send_sockAddr.sin_port = htons (HOST_UDP_PORT);
   }
   if(udp_networkTools_send_socket == -1)
   {
       udp_networkTools_send_socket = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&udp_networkTools_send_sockAddr, 0, sizeof(udp_networkTools_send_sockAddr));
       udp_networkTools_send_sockAddr.sin_family = AF_INET;
	   udp_networkTools_send_sockAddr.sin_port = htons (HOST_UDP_PORT);
   }
   if(udp_networkTools_send_socket_new == -1)
   {
       udp_networkTools_send_socket_new = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&udp_networkTools_send_sockAddr_new, 0, sizeof(udp_networkTools_send_sockAddr_new));
       udp_networkTools_send_sockAddr_new.sin_family = AF_INET;
	   udp_networkTools_send_sockAddr_new.sin_port = htons (NETTOOLS_UDP_PORT);
   }

	//判断命令字
	int cmd_word=0;
	if(len>=PACKAGE_MIN_SIZE)
	{
		cmd_word=(data[0]<<8) + data[1];
	}

   if(g_network_mode == NETWORK_MODE_WAN)
   {
	   	#if ENABLE_TCP_CLIENT
		host_tcp_send_data(data,len);
		#endif

		if(udp_host_addr.sin_addr.s_addr !=0 || multi_host_addr.sin_addr.s_addr != 0)
		{
			if(cmd_word == CMD_HOST_QUERY_SET_IP_INFO || cmd_word == CMD_HOST_QUERY_SET_NET_WORK_MODE || cmd_word == CMD_RATE_PROGRESS)
			{
				if(udp_host_addr.sin_addr.s_addr)
				{
					memcpy(&udp_host_send_sockAddr.sin_addr,&udp_host_addr.sin_addr,sizeof(udp_host_addr.sin_addr));
				}
				else if(multi_host_addr.sin_addr.s_addr)
				{
					memcpy(&udp_host_send_sockAddr.sin_addr,&multi_host_addr.sin_addr,sizeof(multi_host_addr.sin_addr));
				}
				sendto(udp_host_send_socket, data, len, 0,(struct sockaddr*)&udp_host_send_sockAddr, sizeof(udp_host_send_sockAddr));
			}
		}
   }
   else
   {
		if(udp_host_addr.sin_addr.s_addr !=0 || multi_host_addr.sin_addr.s_addr != 0)
		{
			if(udp_host_addr.sin_addr.s_addr)
			{
				memcpy(&udp_host_send_sockAddr.sin_addr,&udp_host_addr.sin_addr,sizeof(udp_host_addr.sin_addr));
			}
			else if(multi_host_addr.sin_addr.s_addr)
			{
				memcpy(&udp_host_send_sockAddr.sin_addr,&multi_host_addr.sin_addr,sizeof(multi_host_addr.sin_addr));
			}
			//ESP_LOGI(TAG, "Udp SendTo:%d.%d.%d.%d\n", (udp_host_send_sockAddr.sin_addr.s_addr)&0xff, (udp_host_send_sockAddr.sin_addr.s_addr>>8)&0xff,(udp_host_send_sockAddr.sin_addr.s_addr>>16)&0xff,(udp_host_send_sockAddr.sin_addr.s_addr>>24)&0xff);
			int sendLen=sendto(udp_host_send_socket, data, len, 0,(struct sockaddr*)&udp_host_send_sockAddr, sizeof(udp_host_send_sockAddr));

			if(sendLen == -1)
			{
				ESP_LOGE(TAG, "host_udp_send_data1 failed: %s", strerror(errno));
			}
		}
   }

	//如果网络配置工具有发送数据过来，则同时发往配置工具，发完后置0，除非下次再收到配置工具发过来的控制指令
	if(udp_networktools_addr.sin_addr.s_addr !=0 )
	{
		memcpy(&udp_networkTools_send_sockAddr.sin_addr,&udp_networktools_addr.sin_addr,sizeof(udp_networktools_addr.sin_addr));
		int sendLen=sendto(udp_networkTools_send_socket, data, len, 0,(struct sockaddr*)&udp_networkTools_send_sockAddr, sizeof(udp_networkTools_send_sockAddr));
		
		memcpy(&udp_networkTools_send_sockAddr_new.sin_addr,&udp_networktools_addr.sin_addr,sizeof(udp_networktools_addr.sin_addr));
		sendLen=sendto(udp_networkTools_send_socket_new, data, len, 0,(struct sockaddr*)&udp_networkTools_send_sockAddr_new, sizeof(udp_networkTools_send_sockAddr_new));

		if(sendLen == -1)
		{
			ESP_LOGE(TAG, "host_udp_send_data2 failed: %s", strerror(errno));
		}
		udp_networktools_addr.sin_addr.s_addr=0;
	}
}



/*********************************************************************
 * @fn      pager_udp_send_data
 *
 * @brief  	发送应答寻呼台数据
 *
 * @param   data--数据缓冲区
 * 			len--数据长度
 *
 * @return  none
*********************************************************************/
void pager_udp_send_data(unsigned char * data,unsigned short len)  
{   
   if(udp_pager_send_socket == -1)
   {
       udp_pager_send_socket = socket(AF_INET, SOCK_DGRAM, 0);
       memset (&udp_pager_send_sockAddr, 0, sizeof(udp_pager_send_sockAddr));
       udp_pager_send_sockAddr.sin_family = AF_INET;
   }
   if(g_network_mode == NETWORK_MODE_LAN)
   {
		udp_pager_send_sockAddr.sin_port = htons (PAGER_UDP_PORT);
		memcpy(&udp_pager_send_sockAddr.sin_addr,&udp_pager_addr.sin_addr,sizeof(udp_pager_addr.sin_addr));
		int sendLen=sendto(udp_pager_send_socket, data, len, 0,(struct sockaddr*)&udp_pager_send_sockAddr, sizeof(udp_pager_send_sockAddr));
		if(sendLen == -1)
		{
			ESP_LOGE(TAG, "pager_udp_send_data failed: %s", strerror(errno));
		}
   }
   else
   {
	   	#if ENABLE_TCP_CLIENT
		host_tcp_send_data(data,len);
		#endif
   }
}


/*********************************************************************
 * @fn      host_udp_client
 *
 * @brief   创建一个UDP客户端线程来处理主机的请求
 *
 * @param   void
 *
 * @return  none
 */
static void host_udp_client(void *pvParameters)
{
	int ret, i;
	int Rxlen;
	int Index = 0;
    int Pkg_Length = 0;
    int Payload_Length = 0;
	fd_set readfd;
	int Read_Size = MAX_BUF_SIZE;
	unsigned char RxBuf[MAX_BUF_SIZE];
	struct sockaddr_in Host_Addr,Pager_Addr,NetTools_Addr;
	socklen_t g_sockaddr_len = sizeof(struct sockaddr_in);
	
	/*填充地址信息*/
	memset(&Host_Addr, 0, sizeof(Host_Addr));            // 清空地址信息
	Host_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
	Host_Addr.sin_port = htons(HOST_UDP_PORT);        // 注册端口
	Host_Addr.sin_addr.s_addr = htonl(INADDR_ANY);    // 允许任何IP地址访问服务器

	memset(&Pager_Addr, 0, sizeof(Pager_Addr));            // 清空地址信息
	Pager_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
	Pager_Addr.sin_port = htons(PAGER_UDP_PORT);        // 注册端口
	Pager_Addr.sin_addr.s_addr = htonl(INADDR_ANY);    // 允许任何IP地址访问服务器

	memset(&NetTools_Addr, 0, sizeof(NetTools_Addr));            // 清空地址信息
	NetTools_Addr.sin_family = AF_INET;                   // 选用TCP/IP协议
	NetTools_Addr.sin_port = htons(NETTOOLS_UDP_PORT);        // 注册端口
	NetTools_Addr.sin_addr.s_addr = htonl(INADDR_ANY);    // 允许任何IP地址访问服务器
	
    int g_host_sockfd,g_pager_sockfd,g_netTools_sockfd;
	/*创建套接字*/
	if ((g_host_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		ESP_LOGE(TAG, "Host udp socket create error: %s", strerror(errno));
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "Host udp socket create success!");
	}
	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	int opt = 1;
	if((setsockopt(g_host_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		ESP_LOGE(TAG, "setsockopt SO_REUSEADDR failed: %s", strerror(errno));
		close(g_host_sockfd);
		vTaskDelete(NULL);
		return;
	}
	/*绑定地址到套接字*/
	ret = bind(g_host_sockfd, (struct sockaddr *)&Host_Addr, sizeof(Host_Addr));
	if (ret < 0)
	{
		ESP_LOGE(TAG, "Host udp socket call bind error: %s", strerror(errno));
		close(g_host_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "Host udp socket bind socket success!");
	}

	/*创建套接字*/
	if ((g_pager_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		ESP_LOGE(TAG, "Pager udp socket create error: %s", strerror(errno));
		close(g_host_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "Pager udp socket create success!");
	}
	
	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	opt = 1;
	if((setsockopt(g_pager_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		ESP_LOGE(TAG, "setsockopt SO_REUSEADDR failed: %s", strerror(errno));
		close(g_host_sockfd);
		close(g_pager_sockfd);
		vTaskDelete(NULL);
		return;
	}

	/*绑定地址到套接字*/
	ret = bind(g_pager_sockfd, (struct sockaddr *)&Pager_Addr, sizeof(Pager_Addr));
	if (ret < 0)
	{
		ESP_LOGE(TAG, "Pager udp socket call bind error: %s", strerror(errno));
		close(g_host_sockfd);
		close(g_pager_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "Pager udp socket bind socket success!");
	}

	/*创建套接字*/
	if ((g_netTools_sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
	{
		ESP_LOGE(TAG, "NetTools udp socket create error: %s", strerror(errno));
		close(g_host_sockfd);
		close(g_pager_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "NetTools udp socket create success!");
	}
	/*设置地址重用,以解决第二次运行时端口占用的问题(SO_REUSEADDR允许完全相同的地址和端口的重复绑定，但这只用于UDP)*/
	opt = 1;
	if((setsockopt(g_netTools_sockfd,SOL_SOCKET,SO_REUSEADDR,&opt,sizeof(opt)))<0)
	{
		ESP_LOGE(TAG, "setsockopt SO_REUSEADDR failed: %s", strerror(errno));
		close(g_host_sockfd);
		close(g_pager_sockfd);
		close(g_netTools_sockfd);
		vTaskDelete(NULL);
		return;
	}
	/*绑定地址到套接字*/
	ret = bind(g_netTools_sockfd, (struct sockaddr *)&NetTools_Addr, sizeof(NetTools_Addr));
	if (ret < 0)
	{
		ESP_LOGE(TAG, "NetTools udp socket call bind error: %s", strerror(errno));
		close(g_host_sockfd);
		close(g_pager_sockfd);
		close(g_netTools_sockfd);
		vTaskDelete(NULL);
		return;
	}
	else
	{
		ESP_LOGI(TAG, "NetTools udp socket bind socket success!");
	}

	
	int recv_type=0;

	while(1)
	{	

		FD_ZERO(&readfd);                // 清空读文件描述集合
		FD_SET(g_host_sockfd, &readfd); // 注册套接字文件描述符
		FD_SET(g_pager_sockfd, &readfd); // 注册套接字文件描述符
		FD_SET(g_netTools_sockfd, &readfd); // 注册套接字文件描述符

		int max_socket1 = g_host_sockfd>g_pager_sockfd ? g_host_sockfd:g_pager_sockfd;
		int fdmax = max_socket1>g_netTools_sockfd ? max_socket1:g_netTools_sockfd;

		ret = select(fdmax+1, &readfd, NULL, NULL, NULL);
		switch(ret)
		{
			case -1 : // 调用出错
				ESP_LOGE(TAG, "Host udp pthread call select error: %s", strerror(errno));
				break;
			
			case 0 : // 超时
				ESP_LOGD(TAG, "Host udp pthread select timeout!!!");
				break;
				
			default : // 有数据可读
				if (FD_ISSET(g_host_sockfd, &readfd))
				{
					recv_type=0;
					Rxlen = recvfrom(g_host_sockfd, &RxBuf[Index], Read_Size, 0, (struct sockaddr *)&Host_Addr, &g_sockaddr_len);
				}
				else if(FD_ISSET(g_pager_sockfd, &readfd))
				{
					recv_type=1;
					Rxlen = recvfrom(g_pager_sockfd, &RxBuf[Index], Read_Size, 0, (struct sockaddr *)&Pager_Addr, &g_sockaddr_len);
				}
				else if(FD_ISSET(g_netTools_sockfd, &readfd))
				{
					recv_type=2;
					Rxlen = recvfrom(g_netTools_sockfd, &RxBuf[Index], Read_Size, 0, (struct sockaddr *)&NetTools_Addr, &g_sockaddr_len);
				}
				if (Rxlen < PACKAGE_MIN_SIZE)
				{
					ESP_LOGE(TAG, "pager udp recvfrom failed: %s", strerror(errno));
					memset(RxBuf, 0, MAX_BUF_SIZE);
					continue;
				}
				else
				{
						Payload_Length = (int)RxBuf[PAYLOAD_START-2]*256 + (int)RxBuf[PAYLOAD_START-1]; // 负载数据长度
						if(Payload_Length>MAX_BUF_SIZE-PACKAGE_MIN_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}
						Pkg_Length = Payload_Length + PACKAGE_MIN_SIZE;     // 整个包长
						if(Pkg_Length>MAX_BUF_SIZE)
						{
							memset(RxBuf, 0, MAX_BUF_SIZE);
							continue;
						}

					/*判断接收长度是否等于包长*/
					if (Rxlen != Pkg_Length)
					{
						memset(RxBuf, 0, MAX_BUF_SIZE);
						continue;
					}
					else
					{
						// 计算校验和
						if (Calculate_XorDat(&RxBuf[PAYLOAD_START], Payload_Length) == RxBuf[Pkg_Length-1])
						{
							//ESP_LOGI(TAG, "Host udp recv:0x%04x",RxBuf[0]*256+RxBuf[1]);
							if(recv_type == 0)
							{
								#if 1
								if(RxBuf[4] == DEVICE_MODEL_HOST)
								{
									tmp_udp_host_addr=Host_Addr;
								}
								else if(RxBuf[4] == DEVICE_MODEL_NETWORK_TOOLS)
								{
									tmp_udp_networktools_addr=Host_Addr;
								}
								#endif
								NetPkg_Add(NET_TYPE_UNICAST_SERVER,RxBuf,Pkg_Length);
							}
							else if(recv_type == 1)
							{
								#if 1
								if(RxBuf[4] == DEVICE_MODEL_PAGING)
								{
									tmp_udp_pager_addr=Pager_Addr;
								}
								#endif
								NetPkg_Add(NET_TYPE_UNICAST_PAGER,RxBuf,Pkg_Length);
							}
							if(recv_type == 2)
							{
								if(RxBuf[4] == DEVICE_MODEL_NETWORK_TOOLS)
								{
									tmp_udp_networktools_addr=NetTools_Addr;
									NetPkg_Add(NET_TYPE_UNICAST_SERVER,RxBuf,Pkg_Length);
								}
							}
						}
						else
						{
							ESP_LOGE(TAG, "Host udp payload check error!!!");
						}
						memset(RxBuf, 0, MAX_BUF_SIZE);
					}
				}
				break;
		}
	}

	close(g_host_sockfd);
	close(g_pager_sockfd);
	close(g_netTools_sockfd);
	vTaskDelete(NULL);
}

/*********************************************************************
 * @fn      start_udp_client
 *
 * @brief   启动udp客户端
 *
 * @param
 *
 *
 * @return
 */
void start_udp_client(void)
{
	BaseType_t ret;
    TaskHandle_t host_udp_pthread;
	
	ret = xTaskCreate(host_udp_client, "host_udp_pthread", 4096, NULL, 5, &host_udp_pthread);
	if (ret != pdPASS)
	{
		ESP_LOGE(TAG, "host_udp_pthread create failed");
	}
	else
	{
		ESP_LOGI(TAG, "Start host_udp_pthread succeed!");
	}
}
